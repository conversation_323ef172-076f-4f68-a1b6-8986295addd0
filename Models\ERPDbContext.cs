using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common;
using FAST_ERP_Backend.Models.Common.Logging;
using FAST_ERP_Backend.Models.Pms;
using FAST_ERP_Backend.Models.Pas;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Interfaces.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection.Emit;
using System.Threading;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Models
{
    public class ERPDbContext : DbContext
    {
        private readonly ILoggerService _logger;
        public ERPDbContext(ILoggerService logger, DbContextOptions<ERPDbContext> options) : base(options)
        {
            _logger = logger;
        }
        #region DbSet
        //Common
        public DbSet<Users> Common_Users { get; set; }
        public DbSet<Department> Common_Departments { get; set; }
        public DbSet<Roles> Common_Roles { get; set; }
        public DbSet<RolesPermissions> Common_RolesPermissions { get; set; }
        public DbSet<AuditLogs> Common_AuditLogs { get; set; }
        public DbSet<SystemMenu> Common_SystemMenu { get; set; }
        public DbSet<SystemGroups> Common_SystemGroups { get; set; }
        public DbSet<EnterpriseGroups> Common_EnterpriseGroups { get; set; }
        public DbSet<Position> Common_Positions { get; set; }
        public DbSet<Division> Common_Divisions { get; set; }
        public DbSet<Unit> Common_Units { get; set; }
        public DbSet<City> Common_Cities { get; set; }
        public DbSet<District> Common_Districts { get; set; }

        public DbSet<EnterpriseImage> Common_EnterpriseImage { get; set; }

        public DbSet<SystemParameters> Common_SystemParameters { get; set; }
        public DbSet<SystemParametersItem> Common_SystemParametersItem { get; set; }
        public DbSet<FileList> Common_FileList { get; set; }
        public DbSet<FileUpload> Common_FileUploads { get; set; }

        //PMS
        public DbSet<Asset> Pms_Assets { get; set; }
        public DbSet<InsuranceUnit> Pms_InsuranceUnits { get; set; }
        public DbSet<Manufacturer> Pms_Manufacturers { get; set; }
        public DbSet<StorageLocation> Pms_StorageLocations { get; set; }
        public DbSet<DepreciationForm> Pms_DepreciationForms { get; set; }
        public DbSet<DepreciationFormDetail> Pms_DepreciationFormDetail { get; set; }
        public DbSet<AssetAccount> Pms_AssetAccounts { get; set; }
        public DbSet<AssetSubAccount> Pms_AssetSubAccounts { get; set; }
        public DbSet<AssetSource> Pms_AssetSources { get; set; }
        public DbSet<AssetCategory> Pms_AssetCategory { get; set; }
        public DbSet<AccessoryEquipment> Pms_AccessoryEquipments { get; set; }
        public DbSet<PmsSystemParameter> Pms_SystemParameters { get; set; }

        public DbSet<AmortizationSource> Pms_AmortizationSources { get; set; }

        public DbSet<AssetStatus> Pms_AssetStatus { get; set; }

        public DbSet<PmsUserRole> PmsUserRoles { get; set; }
        public DbSet<PmsUserRoleMapping> PmsUserRoleMappings { get; set; }

        public DbSet<EquipmentType> Pms_EquipmentType { get; set; }
        public DbSet<AssetCarryOut> Pms_AssetCarryOut { get; set; }
        public DbSet<VendorMaintenance> Pms_VendorMaintenance { get; set; }
        public DbSet<AssetLocationTransfer> Pms_AssetLocationTransfer { get; set; }
        public DbSet<AssetLocationTransferDetail> Pms_AssetLocationTransferDetail { get; set; }

        //Pas
        public DbSet<Employee> Pas_Employee { get; set; }
        public DbSet<Education> Pas_Education { get; set; }
        public DbSet<Train> Pas_Train { get; set; }
        public DbSet<Examination> Pas_Examination { get; set; }
        public DbSet<Certification> Pas_Certification { get; set; }
        public DbSet<Undergo> Pas_Undergo { get; set; }
        public DbSet<Ensure> Pas_Ensure { get; set; }
        public DbSet<Suspend> Pas_Suspend { get; set; }
        public DbSet<Salary> Pas_Salary { get; set; }
        public DbSet<Hensure> Pas_Hensure { get; set; }
        public DbSet<Dependent> Pas_Dependent { get; set; }
        public DbSet<PerformancePointGroup> Pas_PerformancePointGroup { get; set; }
        public DbSet<PerformancePointType> Pas_PerformancePointType { get; set; }
        public DbSet<PerformancePointRecord> Pas_PerformancePointRecord { get; set; }
        public DbSet<RegularSalaryItem> Pas_RegularSalaryItem { get; set; }
        public DbSet<EmployeeRegularSalary> Pas_EmployeeRegularSalary { get; set; }
        public DbSet<SalaryPoint> Pas_SalaryPoint { get; set; }
        public DbSet<InsuranceGrade> Pas_InsuranceGrade { get; set; }
        public DbSet<InsuranceHistory> Pas_InsuranceHistory { get; set; }
        public DbSet<Promotion> Pas_Promotion { get; set; }
        public DbSet<ExpenseDepartmentChange> Pas_ExpenseDepartmentChange { get; set; }
        public DbSet<ServiceDepartmentChange> Pas_ServiceDepartmentChange { get; set; }


        //Ims
        /// <summary> 商業夥伴 </summary>
        public DbSet<Partner> Ims_Partner { get; set; }
        /// <summary> 自然人 </summary>
        public DbSet<IndividualDetail> Ims_IndividualDetail { get; set; }
        /// <summary> 法人 </summary>
        public DbSet<EnterpriseDetail> Ims_EnterpriseDetail { get; set; }
        /// <summary> 客戶詳細資訊 </summary>
        public DbSet<CustomerDetail> Ims_CustomerDetail { get; set; }
        /// <summary> 供應商詳細資訊 </summary>
        public DbSet<SupplierDetail> Ims_SupplierDetail { get; set; }
        /// <summary> 商業夥伴聯絡人聯結表 </summary>
        public DbSet<PartnerContact> Ims_PartnerContact { get; set; }
        /// <summary> 聯絡人 </summary>
        public DbSet<Contact> Ims_Contact { get; set; }
        /// <summary> 聯絡人角色 </summary>
        public DbSet<ContactRole> Ims_ContactRole { get; set; }
        /// <summary> 商業夥伴地址 </summary>
        public DbSet<PartnerAddress> Ims_PartnerAddress { get; set; }
        /// <summary> 庫存品 </summary>
        public DbSet<Item> Ims_Item { get; set; }
        /// <summary> 庫存品分類 </summary>
        public DbSet<ItemCategory> Ims_ItemCategory { get; set; }
        /// <summary> 主分類 </summary>
        public DbSet<ItemPrice> Ims_ItemPrice { get; set; }
        /// <summary> 價格類型 </summary>
        public DbSet<PriceType> Ims_PriceType { get; set; }
        /// <summary> 客戶分類 </summary>
        public DbSet<CustomerCategory> Ims_CustomerCategory { get; set; }
        /// <summary> 供應商分類 </summary>
        public DbSet<SupplierCategory> Ims_SupplierCategory { get; set; }

        #endregion

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // Design-time configuration - use a default connection string
                // This will be used by EF tools when no DI container is available
                optionsBuilder.UseSqlServer("Data Source=.\\SQLEXPRESS2022;Initial Catalog=FAST_ERP;Persist Security Info=True;User ID=sa;Password=********;Encrypt=True;TrustServerCertificate=True;");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            new DbSeederData().SeedData(modelBuilder);

            //資料表關聯設定
            modelBuilder.ConfigureSystemMenuRelationships();

            // Entity Framework Core 生成 SQL 查詢時，會自動將過濾條件加入到 WHERE 子句中
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(ModelBaseEntity).IsAssignableFrom(entityType.ClrType))
                {
                    var parameter = Expression.Parameter(entityType.ClrType, "e");
                    var property = Expression.Property(parameter, nameof(ModelBaseEntity.IsDeleted));
                    var filter = Expression.Lambda(
                        Expression.Equal(
                            Expression.Convert(property, typeof(bool?)),
                            Expression.Constant(false, typeof(bool?))),
                        parameter);

                    modelBuilder.Entity(entityType.ClrType).HasQueryFilter(filter);
                }
            }

            // PmsUserRole 配置
            modelBuilder.Entity<PmsUserRole>()
                .HasQueryFilter(e => !e.IsDeleted);

            // PmsUserRoleMapping 配置
            modelBuilder.Entity<PmsUserRoleMapping>()
                .HasQueryFilter(e => !e.IsDeleted);

            // PmsUserRoleMapping 與 Users 關聯
            modelBuilder.Entity<PmsUserRoleMapping>()
                .HasOne(m => m.User)
                .WithMany()
                .HasForeignKey(m => m.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // PmsUserRoleMapping 與 PmsUserRole 關聯
            modelBuilder.Entity<PmsUserRoleMapping>()
                .HasOne(m => m.PmsUserRole)
                .WithMany()
                .HasForeignKey(m => m.PmsUserRoleId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ItemPrice>(Entity =>
            {
                Entity
                .HasOne(ip => ip.Item)
                .WithMany(ip => ip.Prices)
                .HasForeignKey(i => i.ItemID)
                .OnDelete(DeleteBehavior.Restrict);
                Entity
                .HasOne(ip => ip.PriceType)
                .WithMany()
                .HasForeignKey(ip => ip.PriceTypeID)
                .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Item>(Entity =>
            {
                Entity
                .HasOne(i => i.ItemCategory)
                .WithMany(ic => ic.Items)
                .HasForeignKey(i => i.ItemCategoryID)
                .OnDelete(DeleteBehavior.Restrict);
            });

            // Ims 模組的 Partner 相關配置
            modelBuilder.Entity<Partner>(entity =>
            {
                // Partner 與 PartnerAddress 的一對多關聯
                entity.HasMany(p => p.Addresses)
                      .WithOne(pa => pa.Partner)
                      .HasForeignKey(pa => pa.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 PartnerAddress 也會被刪除

                // Partner 與 IndividualDetail 的一對一關聯
                entity.HasOne(p => p.IndividualDetail)
                      .WithOne()
                      .HasForeignKey<IndividualDetail>(id => id.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 IndividualDetail 也會被刪除

                // Partner 與 EnterpriseDetail 的一對一關聯
                entity.HasOne(p => p.EnterpriseDetail)
                      .WithOne()
                      .HasForeignKey<EnterpriseDetail>(od => od.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 EnterpriseDetail 也會被刪除

                // Partner 與 CustomerDetail 的一對一關聯
                entity.HasOne(p => p.CustomerDetail)
                      .WithOne()
                      .HasForeignKey<CustomerDetail>(cd => cd.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 CustomerDetail 也會被刪除

                // Partner 與 SupplierDetail 的一對一關聯
                entity.HasOne(p => p.SupplierDetail)
                      .WithOne()
                      .HasForeignKey<SupplierDetail>(sd => sd.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的 SupplierDetail 也會被刪除
            });

            // SupplierDetail 與 SupplierCategory 的多對一關聯
            modelBuilder.Entity<SupplierDetail>(entity =>
            {
                entity.HasOne(sd => sd.SupplierCategory)
                      .WithMany()
                      .HasForeignKey(sd => sd.SupplierCategoryID)
                      .OnDelete(DeleteBehavior.Restrict); // 當 SupplierCategory 被刪除時，不允許刪除 (保護資料完整性)
            });

            // CustomerDetail 與 CustomerCategory 的多對一關聯
            modelBuilder.Entity<CustomerDetail>(entity =>
            {
                entity.HasOne(cd => cd.CustomerCategory)
                      .WithMany()
                      .HasForeignKey(cd => cd.CustomerCategoryID)
                      .OnDelete(DeleteBehavior.Restrict); // 當 CustomerCategory 被刪除時，不允許刪除 (保護資料完整性)
            });

            // Partner 與 Contact 的多對多關聯 (透過 PartnerContact 聯結表)
            modelBuilder.Entity<PartnerContact>(entity =>
            {
                entity.HasKey(pc => new { pc.PartnerID, pc.ContactID, pc.ContactRoleID }); // 定義複合主鍵

                entity.HasOne(pc => pc.Partner)
                      .WithMany(p => p.PartnerContacts)
                      .HasForeignKey(pc => pc.PartnerID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Partner 被刪除時，相關的聯結記錄也會被刪除

                entity.HasOne(pc => pc.Contact)
                      .WithMany(c => c.PartnerContacts)
                      .HasForeignKey(pc => pc.ContactID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 Contact 被刪除時，相關的聯結記錄也會被刪除

                entity.HasOne(pc => pc.ContactRole)
                      .WithMany(cr => cr.PartnerContacts)
                      .HasForeignKey(pc => pc.ContactRoleID)
                      .OnDelete(DeleteBehavior.Cascade); // 當 ContactRole 被刪除時，相關的聯結記錄也會被刪除
            });
        }

        /// <summary>
        /// 覆寫 SaveChangesAsync 以自動偵測資料變化並記錄日誌
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>受影響的行數</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            var changedEntries = CaptureChangedEntries();
            var transactionId = Guid.NewGuid().ToString();

            try
            {
                var result = await base.SaveChangesAsync(cancellationToken);

                if (result > 0 && changedEntries.Any())
                {
                    await LogEntityChangesAsync(changedEntries, transactionId);
                }

                return result;
            }
            catch (Exception ex)
            {
                if (_logger != null)
                {
                    await _logger.LogErrorAsync(
                        $"資料庫保存變更失敗，交易ID: {transactionId}",
                        ex,
                        "ERPDbContext"
                    );
                }
                throw;
            }
        }

        /// <summary> 捕獲變更的實體 </summary>
        /// <returns>變更的實體列表</returns>
        private List<EntityChangeInfo> CaptureChangedEntries()
        {
            return ChangeTracker.Entries()
                .Where(e => e.State != EntityState.Unchanged)
                .Select(e => new EntityChangeInfo
                {
                    EntityType = e.Entity.GetType().Name,
                    State = e.State,
                    Original = e.State == EntityState.Added ? null : e.OriginalValues.ToObject(),
                    Current = e.Entity,
                    UserId = ExtractUserId(e.Entity)
                })
                .ToList();
        }

        /// <summary> 記錄實體變更日誌 </summary>
        /// <param name="changedEntries">變更的實體</param>
        /// <param name="transactionId">交易ID</param>
        private async Task LogEntityChangesAsync(List<EntityChangeInfo> changedEntries, string transactionId)
        {
            if (_logger == null) return; // Skip logging during design-time

            try
            {
                // 建立詳細的變更日誌，保留關鍵資訊
                var changeLog = new
                {
                    transactionId = transactionId,
                    source = "ERPDbContext",
                    changeTime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    totalChanges = changedEntries.Count,
                    entities = changedEntries.Select(entry => new
                    {
                        entityType = entry.EntityType,
                        entityId = GetEntityId(entry.Current),
                        entityState = entry.State.ToString(),
                        userId = entry.UserId,
                        timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                        // 根據狀態記錄詳細的資料，特別處理 Partner 相關實體
                        data = entry.State switch
                        {
                            EntityState.Added => IsPartnerRelatedEntity(entry.Current)
                                ? CreateDetailedPartnerRelatedEntityData(entry.Current, entry.State)
                                : CreateDetailedAddedEntityData(entry.Current),
                            EntityState.Modified => IsPartnerRelatedEntity(entry.Current)
                                ? CreateDetailedPartnerRelatedEntityData(entry.Current, entry.State, entry.Original)
                                : CreateDetailedModifiedEntityData(entry),
                            EntityState.Deleted => IsPartnerRelatedEntity(entry.Current)
                                ? CreateDetailedPartnerRelatedEntityData(entry.Current, entry.State)
                                : CreateDetailedDeletedEntityData(entry.Current),
                            _ => new { }
                        },

                        // 新增：Partner 相關實體的關聯上下文
                        relatedContext = IsPartnerRelatedEntity(entry.Current)
                            ? ExtractPartnerRelatedContext(entry.Current, entry.State)
                            : null
                    }).ToList()
                };

                var message = $"實體變更記錄 - 交易ID: {transactionId}, 變更數量: {changeLog.totalChanges}";

                // 記錄詳細的變更日誌
                await _logger.LogDataAsync(message, changeLog, transactionId, "ERPDbContext");
            }
            catch (Exception ex)
            {
                // 日誌記錄失敗不應該影響主要業務流程
                // 使用 Console.WriteLine 避免遞歸日誌錯誤
                Console.WriteLine($"[ERPDbContext] 實體變更日誌記錄失敗: {ex.Message}");
            }
        }

        /// <summary> 提取用戶ID </summary>
        /// <param name="entity">實體對象</param>
        /// <returns>用戶ID</returns>
        private static string? ExtractUserId(object entity)
        {
            try
            {
                return entity.GetType()
                    .GetProperty("UpdateUserId")?
                    .GetValue(entity, null)?
                    .ToString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary> 提取實體ID </summary>
        /// <param name="entity">實體對象</param>
        /// <returns>實體ID</returns>
        private static string GetEntityId(object entity)
        {
            try
            {
                var idProperty = entity.GetType().GetProperties()
                    .FirstOrDefault(p => p.Name.EndsWith("ID") || p.Name.EndsWith("Id") ||
                                        p.IsDefined(typeof(System.ComponentModel.DataAnnotations.KeyAttribute), false));

                if (idProperty != null)
                {
                    var value = idProperty.GetValue(entity);
                    return value?.ToString() ?? "Unknown";
                }
            }
            catch
            {
                // 忽略錯誤，返回預設值
            }

            return "Unknown";
        }

        /// <summary> 建立新增實體的資料記錄 </summary>
        /// <param name="entity">實體對象</param>
        /// <returns>新增實體資料</returns>
        private static object CreateAddedEntityData(object entity)
        {
            try
            {
                var properties = entity.GetType().GetProperties()
                    .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                    .Take(8) // 限制最多 8 個屬性
                    .ToDictionary(
                        p => p.Name,
                        p => {
                            try
                            {
                                var value = p.GetValue(entity);
                                return FormatPropertyValue(value);
                            }
                            catch
                            {
                                return "[讀取失敗]";
                            }
                        }
                    );

                return properties;
            }
            catch
            {
                return new { error = "無法讀取實體屬性" };
            }
        }

        /// <summary> 建立修改實體的資料記錄 </summary>
        /// <param name="entry">實體變更資訊</param>
        /// <returns>修改實體資料</returns>
        private static object CreateModifiedEntityData(EntityChangeInfo entry)
        {
            try
            {
                var changes = new Dictionary<string, object>();

                // 比較原始和當前物件的屬性
                var properties = entry.Current.GetType().GetProperties()
                    .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                    .Take(10); // 限制最多 10 個屬性

                foreach (var prop in properties)
                {
                    try
                    {
                        var currentValue = prop.GetValue(entry.Current);
                        var originalValue = entry.Original != null ? prop.GetValue(entry.Original) : null;

                        // 只記錄有變更的屬性
                        if (!Equals(currentValue, originalValue))
                        {
                            changes[prop.Name] = new
                            {
                                from = FormatPropertyValue(originalValue),
                                to = FormatPropertyValue(currentValue)
                            };
                        }
                    }
                    catch
                    {
                        // 忽略讀取失敗的屬性
                    }
                }

                return new { changes };
            }
            catch
            {
                return new { error = "無法比較實體變更" };
            }
        }

        /// <summary> 建立刪除實體的資料記錄 </summary>
        /// <param name="entity">實體對象</param>
        /// <returns>刪除實體資料</returns>
        private static object CreateDeletedEntityData(object entity)
        {
            try
            {
                return new
                {
                    entityId = GetEntityId(entity),
                    deletedAt = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
            catch
            {
                return new { error = "無法讀取刪除實體資訊" };
            }
        }

        /// <summary> 格式化屬性值 </summary>
        /// <param name="value">屬性值</param>
        /// <returns>格式化後的值</returns>
        private static object? FormatPropertyValue(object? value)
        {
            return value switch
            {
                null => null,
                Guid guid => guid.ToString(),
                DateTime dateTime => dateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("yyyy-MM-dd HH:mm:ss"),
                decimal dec => dec.ToString("F2"),
                long timestamp when timestamp > 1000000000 => DateTimeOffset.FromUnixTimeMilliseconds(timestamp).ToString("yyyy-MM-dd HH:mm:ss"),
                _ => value
            };
        }

        /// <summary> 建立詳細的新增實體資料記錄 </summary>
        /// <param name="entity">實體對象</param>
        /// <returns>詳細的新增實體資料</returns>
        private static object CreateDetailedAddedEntityData(object entity)
        {
            try
            {
                var properties = entity.GetType().GetProperties()
                    .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                    .Where(p => !IsNavigationProperty(p, entity.GetType())) // 過濾導航屬性
                    .Take(15) // 增加到 15 個屬性
                    .ToDictionary(
                        p => p.Name,
                        p => {
                            try
                            {
                                var value = p.GetValue(entity);
                                return FormatPropertyValue(value);
                            }
                            catch
                            {
                                return "[讀取失敗]";
                            }
                        }
                    );

                return new {
                    action = "新增",
                    properties = properties,
                    propertyCount = properties.Count
                };
            }
            catch
            {
                return new {
                    action = "新增",
                    error = "無法讀取實體屬性"
                };
            }
        }

        /// <summary> 建立詳細的修改實體資料記錄 </summary>
        /// <param name="entry">實體變更資訊</param>
        /// <returns>詳細的修改實體資料</returns>
        private static object CreateDetailedModifiedEntityData(EntityChangeInfo entry)
        {
            try
            {
                var changes = new Dictionary<string, object>();
                var allProperties = new Dictionary<string, object>();

                // 比較原始和當前物件的屬性
                var properties = entry.Current.GetType().GetProperties()
                    .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                    .Where(p => !IsNavigationProperty(p, entry.Current.GetType())); // 過濾導航屬性

                foreach (var prop in properties)
                {
                    try
                    {
                        var currentValue = prop.GetValue(entry.Current);
                        var originalValue = entry.Original != null ? prop.GetValue(entry.Original) : null;

                        // 記錄所有屬性的當前值
                        allProperties[prop.Name] = FormatPropertyValue(currentValue);

                        // 只記錄有變更的屬性
                        if (!Equals(currentValue, originalValue))
                        {
                            changes[prop.Name] = new
                            {
                                from = FormatPropertyValue(originalValue),
                                to = FormatPropertyValue(currentValue)
                            };
                        }
                    }
                    catch
                    {
                        // 忽略讀取失敗的屬性
                    }
                }

                return new {
                    action = "修改",
                    changes = changes,
                    changedCount = changes.Count,
                    currentProperties = allProperties.Take(10).ToDictionary(kvp => kvp.Key, kvp => kvp.Value), // 限制顯示的屬性數量
                    totalProperties = allProperties.Count
                };
            }
            catch
            {
                return new {
                    action = "修改",
                    error = "無法比較實體變更"
                };
            }
        }

        /// <summary> 建立詳細的刪除實體資料記錄 </summary>
        /// <param name="entity">實體對象</param>
        /// <returns>詳細的刪除實體資料</returns>
        private static object CreateDetailedDeletedEntityData(object entity)
        {
            try
            {
                // 記錄刪除前的關鍵屬性
                var keyProperties = entity.GetType().GetProperties()
                    .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                    .Where(p => !IsNavigationProperty(p, entity.GetType())) // 過濾導航屬性
                    .Where(p => p.Name.Contains("ID") || p.Name.Contains("Name") || p.Name.Contains("Code") || p.Name.Contains("NO"))
                    .Take(8)
                    .ToDictionary(
                        p => p.Name,
                        p => {
                            try
                            {
                                var value = p.GetValue(entity);
                                return FormatPropertyValue(value);
                            }
                            catch
                            {
                                return "[讀取失敗]";
                            }
                        }
                    );

                return new
                {
                    action = "刪除",
                    entityId = GetEntityId(entity),
                    deletedAt = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    keyProperties = keyProperties
                };
            }
            catch
            {
                return new {
                    action = "刪除",
                    error = "無法讀取刪除實體資訊"
                };
            }
        }

        /// <summary> 檢查是否為導航屬性 </summary>
        /// <param name="property">屬性資訊</param>
        /// <param name="entityType">實體類型</param>
        /// <returns>是否為導航屬性</returns>
        private static bool IsNavigationProperty(System.Reflection.PropertyInfo property, Type entityType)
        {
            // 檢查是否有 EF 相關屬性
            if (property.IsDefined(typeof(System.ComponentModel.DataAnnotations.Schema.ForeignKeyAttribute), false) ||
                property.IsDefined(typeof(System.ComponentModel.DataAnnotations.Schema.InversePropertyAttribute), false))
                return true;

            // 檢查是否為虛擬屬性且為實體類型或集合
            var getMethod = property.GetGetMethod();
            if (getMethod?.IsVirtual == true && !getMethod.IsFinal)
            {
                var propertyType = property.PropertyType;

                // 檢查是否為實體類型
                if (typeof(ModelBaseEntity).IsAssignableFrom(propertyType))
                    return true;

                // 檢查是否為實體集合
                if (propertyType.IsGenericType)
                {
                    var genericType = propertyType.GetGenericArguments().FirstOrDefault();
                    if (genericType != null && typeof(ModelBaseEntity).IsAssignableFrom(genericType))
                        return true;
                }
            }

            return false;
        }

        /// <summary> 檢查是否為 Partner 相關實體 </summary>
        /// <param name="entity">實體對象</param>
        /// <returns>是否為 Partner 相關實體</returns>
        private static bool IsPartnerRelatedEntity(object entity)
        {
            var entityType = entity.GetType();
            return entityType.Name == "Partner" ||
                   entityType.Name == "IndividualDetail" ||
                   entityType.Name == "EnterpriseDetail" ||
                   entityType.Name == "CustomerDetail" ||
                   entityType.Name == "SupplierDetail" ||
                   entityType.Name == "PartnerAddress" ||
                   entityType.Name == "PartnerContact";
        }

        /// <summary> 建立詳細的 Partner 相關實體資料記錄 </summary>
        /// <param name="entity">實體對象</param>
        /// <param name="state">實體狀態</param>
        /// <param name="original">原始實體（僅修改時提供）</param>
        /// <returns>詳細的 Partner 相關實體資料</returns>
        private object CreateDetailedPartnerRelatedEntityData(object entity, EntityState state, object? original = null)
        {
            try
            {
                var entityType = entity.GetType();
                var result = new Dictionary<string, object>
                {
                    ["entityType"] = entityType.Name,
                    ["action"] = state.ToString(),
                    ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
                };

                switch (state)
                {
                    case EntityState.Added:
                        result["data"] = ExtractPartnerEntitySnapshot(entity);
                        break;

                    case EntityState.Modified:
                        result["beforeSnapshot"] = original != null ? ExtractPartnerEntitySnapshot(original) : null;
                        result["afterSnapshot"] = ExtractPartnerEntitySnapshot(entity);
                        result["changes"] = ExtractPartnerEntityChanges(entity, original);
                        break;

                    case EntityState.Deleted:
                        result["deletedSnapshot"] = ExtractPartnerEntitySnapshot(entity);
                        break;
                }

                // 如果是 Partner 主實體，嘗試載入完整的關聯資料
                if (entityType.Name == "Partner")
                {
                    result["fullPartnerHierarchy"] = ExtractFullPartnerHierarchy(entity);
                }

                return result;
            }
            catch (Exception ex)
            {
                return new
                {
                    entityType = entity.GetType().Name,
                    action = state.ToString(),
                    error = $"Partner 相關實體資料提取失敗: {ex.Message}",
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")
                };
            }
        }

        /// <summary> 提取 Partner 實體快照 </summary>
        /// <param name="entity">實體對象</param>
        /// <returns>實體快照</returns>
        private static Dictionary<string, object?> ExtractPartnerEntitySnapshot(object entity)
        {
            var snapshot = new Dictionary<string, object?>();
            var properties = entity.GetType().GetProperties()
                .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                .Where(p => !IsNavigationProperty(p, entity.GetType()));

            foreach (var prop in properties)
            {
                try
                {
                    var value = prop.GetValue(entity);
                    snapshot[prop.Name] = FormatPropertyValue(value);
                }
                catch
                {
                    snapshot[prop.Name] = "[讀取失敗]";
                }
            }

            return snapshot;
        }

        /// <summary> 提取 Partner 實體變更 </summary>
        /// <param name="current">當前實體</param>
        /// <param name="original">原始實體</param>
        /// <returns>變更資訊</returns>
        private static Dictionary<string, object> ExtractPartnerEntityChanges(object current, object? original)
        {
            var changes = new Dictionary<string, object>();

            if (original == null) return changes;

            var properties = current.GetType().GetProperties()
                .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                .Where(p => !IsNavigationProperty(p, current.GetType()));

            foreach (var prop in properties)
            {
                try
                {
                    var currentValue = prop.GetValue(current);
                    var originalValue = prop.GetValue(original);

                    if (!Equals(currentValue, originalValue))
                    {
                        changes[prop.Name] = new
                        {
                            from = FormatPropertyValue(originalValue),
                            to = FormatPropertyValue(currentValue)
                        };
                    }
                }
                catch
                {
                    // 忽略讀取失敗的屬性
                }
            }

            return changes;
        }

        /// <summary> 提取完整的 Partner 階層結構 </summary>
        /// <param name="partnerEntity">Partner 實體</param>
        /// <returns>完整的階層結構</returns>
        private Dictionary<string, object?> ExtractFullPartnerHierarchy(object partnerEntity)
        {
            var hierarchy = new Dictionary<string, object?>();

            try
            {
                // 提取 Partner 基本資訊
                hierarchy["partner"] = ExtractPartnerEntitySnapshot(partnerEntity);

                // 嘗試載入相關實體（如果已載入）
                var partnerType = partnerEntity.GetType();

                // IndividualDetail
                var individualDetailProp = partnerType.GetProperty("IndividualDetail");
                if (individualDetailProp != null)
                {
                    var individualDetail = individualDetailProp.GetValue(partnerEntity);
                    hierarchy["individualDetail"] = individualDetail != null ? ExtractPartnerEntitySnapshot(individualDetail) : null;
                }

                // EnterpriseDetail
                var enterpriseDetailProp = partnerType.GetProperty("EnterpriseDetail");
                if (enterpriseDetailProp != null)
                {
                    var enterpriseDetail = enterpriseDetailProp.GetValue(partnerEntity);
                    hierarchy["enterpriseDetail"] = enterpriseDetail != null ? ExtractPartnerEntitySnapshot(enterpriseDetail) : null;
                }

                // CustomerDetail
                var customerDetailProp = partnerType.GetProperty("CustomerDetail");
                if (customerDetailProp != null)
                {
                    var customerDetail = customerDetailProp.GetValue(partnerEntity);
                    hierarchy["customerDetail"] = customerDetail != null ? ExtractPartnerEntitySnapshot(customerDetail) : null;
                }

                // SupplierDetail
                var supplierDetailProp = partnerType.GetProperty("SupplierDetail");
                if (supplierDetailProp != null)
                {
                    var supplierDetail = supplierDetailProp.GetValue(partnerEntity);
                    hierarchy["supplierDetail"] = supplierDetail != null ? ExtractPartnerEntitySnapshot(supplierDetail) : null;
                }

                // Addresses (取前3個)
                var addressesProp = partnerType.GetProperty("Addresses");
                if (addressesProp != null)
                {
                    var addresses = addressesProp.GetValue(partnerEntity);
                    if (addresses is IEnumerable<object> addressList)
                    {
                        hierarchy["addresses"] = addressList.Take(3)
                            .Select(addr => ExtractPartnerEntitySnapshot(addr))
                            .ToList();
                    }
                }

                // PartnerContacts (取前3個)
                var contactsProp = partnerType.GetProperty("PartnerContacts");
                if (contactsProp != null)
                {
                    var contacts = contactsProp.GetValue(partnerEntity);
                    if (contacts is IEnumerable<object> contactList)
                    {
                        hierarchy["partnerContacts"] = contactList.Take(3)
                            .Select(contact => ExtractPartnerEntitySnapshot(contact))
                            .ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                hierarchy["extractionError"] = $"階層結構提取失敗: {ex.Message}";
            }

            return hierarchy;
        }

        /// <summary> 提取 Partner 相關實體的關聯上下文 </summary>
        /// <param name="entity">實體對象</param>
        /// <param name="state">實體狀態</param>
        /// <returns>關聯上下文資訊</returns>
        private Dictionary<string, object?> ExtractPartnerRelatedContext(object entity, EntityState state)
        {
            var context = new Dictionary<string, object?>();

            try
            {
                var entityType = entity.GetType();
                context["entityTypeName"] = entityType.Name;
                context["entityState"] = state.ToString();

                // 根據實體類型提取不同的關聯資訊
                switch (entityType.Name)
                {
                    case "Partner":
                        context.Merge(ExtractPartnerContext(entity));
                        break;
                    case "IndividualDetail":
                        context.Merge(ExtractIndividualDetailContext(entity));
                        break;
                    case "EnterpriseDetail":
                        context.Merge(ExtractEnterpriseDetailContext(entity));
                        break;
                    case "CustomerDetail":
                        context.Merge(ExtractCustomerDetailContext(entity));
                        break;
                    case "SupplierDetail":
                        context.Merge(ExtractSupplierDetailContext(entity));
                        break;
                    case "PartnerAddress":
                        context.Merge(ExtractPartnerAddressContext(entity));
                        break;
                    case "PartnerContact":
                        context.Merge(ExtractPartnerContactContext(entity));
                        break;
                }

                // 嘗試找到相關的 Partner 主實體
                var relatedPartner = FindRelatedPartnerEntity(entity);
                if (relatedPartner != null)
                {
                    context["relatedPartnerInfo"] = new
                    {
                        partnerId = GetEntityId(relatedPartner),
                        partnerName = GetPartnerName(relatedPartner),
                        partnerType = GetPartnerType(relatedPartner)
                    };
                }
            }
            catch (Exception ex)
            {
                context["extractionError"] = $"關聯上下文提取失敗: {ex.Message}";
            }

            return context;
        }

        /// <summary> 提取 Partner 實體上下文 </summary>
        private static Dictionary<string, object?> ExtractPartnerContext(object partner)
        {
            var context = new Dictionary<string, object?>();

            try
            {
                var partnerType = partner.GetType();
                var nameProperty = partnerType.GetProperty("Name");
                var typeProperty = partnerType.GetProperty("Type");
                var isStopProperty = partnerType.GetProperty("IsStop");

                context["partnerName"] = nameProperty?.GetValue(partner)?.ToString();
                context["partnerType"] = typeProperty?.GetValue(partner)?.ToString();
                context["isStop"] = isStopProperty?.GetValue(partner);
                context["contextType"] = "主要合作夥伴實體";
            }
            catch
            {
                context["contextType"] = "Partner 上下文提取失敗";
            }

            return context;
        }

        /// <summary> 提取 IndividualDetail 實體上下文 </summary>
        private static Dictionary<string, object?> ExtractIndividualDetailContext(object individualDetail)
        {
            var context = new Dictionary<string, object?>();

            try
            {
                var type = individualDetail.GetType();
                var firstNameProperty = type.GetProperty("FirstName");
                var lastNameProperty = type.GetProperty("LastName");
                var partnerIdProperty = type.GetProperty("PartnerID");

                context["firstName"] = firstNameProperty?.GetValue(individualDetail)?.ToString();
                context["lastName"] = lastNameProperty?.GetValue(individualDetail)?.ToString();
                context["parentPartnerId"] = partnerIdProperty?.GetValue(individualDetail)?.ToString();
                context["contextType"] = "個人詳細資料";
            }
            catch
            {
                context["contextType"] = "IndividualDetail 上下文提取失敗";
            }

            return context;
        }

        /// <summary> 提取 EnterpriseDetail 實體上下文 </summary>
        private static Dictionary<string, object?> ExtractEnterpriseDetailContext(object enterpriseDetail)
        {
            var context = new Dictionary<string, object?>();

            try
            {
                var type = enterpriseDetail.GetType();
                var companyNameProperty = type.GetProperty("CompanyName");
                var taxIdProperty = type.GetProperty("TaxID");
                var partnerIdProperty = type.GetProperty("PartnerID");

                context["companyName"] = companyNameProperty?.GetValue(enterpriseDetail)?.ToString();
                context["taxId"] = taxIdProperty?.GetValue(enterpriseDetail)?.ToString();
                context["parentPartnerId"] = partnerIdProperty?.GetValue(enterpriseDetail)?.ToString();
                context["contextType"] = "企業詳細資料";
            }
            catch
            {
                context["contextType"] = "EnterpriseDetail 上下文提取失敗";
            }

            return context;
        }

        /// <summary> 提取 CustomerDetail 實體上下文 </summary>
        private static Dictionary<string, object?> ExtractCustomerDetailContext(object customerDetail)
        {
            var context = new Dictionary<string, object?>();

            try
            {
                var type = customerDetail.GetType();
                var partnerIdProperty = type.GetProperty("PartnerID");
                var customerCategoryIdProperty = type.GetProperty("CustomerCategoryID");

                context["parentPartnerId"] = partnerIdProperty?.GetValue(customerDetail)?.ToString();
                context["customerCategoryId"] = customerCategoryIdProperty?.GetValue(customerDetail)?.ToString();
                context["contextType"] = "客戶詳細資料";
            }
            catch
            {
                context["contextType"] = "CustomerDetail 上下文提取失敗";
            }

            return context;
        }

        /// <summary> 提取 SupplierDetail 實體上下文 </summary>
        private static Dictionary<string, object?> ExtractSupplierDetailContext(object supplierDetail)
        {
            var context = new Dictionary<string, object?>();

            try
            {
                var type = supplierDetail.GetType();
                var partnerIdProperty = type.GetProperty("PartnerID");
                var supplierCategoryIdProperty = type.GetProperty("SupplierCategoryID");

                context["parentPartnerId"] = partnerIdProperty?.GetValue(supplierDetail)?.ToString();
                context["supplierCategoryId"] = supplierCategoryIdProperty?.GetValue(supplierDetail)?.ToString();
                context["contextType"] = "供應商詳細資料";
            }
            catch
            {
                context["contextType"] = "SupplierDetail 上下文提取失敗";
            }

            return context;
        }

        /// <summary> 提取 PartnerAddress 實體上下文 </summary>
        private static Dictionary<string, object?> ExtractPartnerAddressContext(object partnerAddress)
        {
            var context = new Dictionary<string, object?>();

            try
            {
                var type = partnerAddress.GetType();
                var partnerIdProperty = type.GetProperty("PartnerID");
                var addressTypeProperty = type.GetProperty("AddressType");

                context["parentPartnerId"] = partnerIdProperty?.GetValue(partnerAddress)?.ToString();
                context["addressType"] = addressTypeProperty?.GetValue(partnerAddress)?.ToString();
                context["contextType"] = "合作夥伴地址";
            }
            catch
            {
                context["contextType"] = "PartnerAddress 上下文提取失敗";
            }

            return context;
        }

        /// <summary> 提取 PartnerContact 實體上下文 </summary>
        private static Dictionary<string, object?> ExtractPartnerContactContext(object partnerContact)
        {
            var context = new Dictionary<string, object?>();

            try
            {
                var type = partnerContact.GetType();
                var partnerIdProperty = type.GetProperty("PartnerID");
                var contactTypeProperty = type.GetProperty("ContactType");

                context["parentPartnerId"] = partnerIdProperty?.GetValue(partnerContact)?.ToString();
                context["contactType"] = contactTypeProperty?.GetValue(partnerContact)?.ToString();
                context["contextType"] = "合作夥伴聯絡資訊";
            }
            catch
            {
                context["contextType"] = "PartnerContact 上下文提取失敗";
            }

            return context;
        }

        /// <summary> 尋找相關的 Partner 主實體 </summary>
        private object? FindRelatedPartnerEntity(object entity)
        {
            try
            {
                var entityType = entity.GetType();

                // 如果本身就是 Partner，直接返回
                if (entityType.Name == "Partner")
                {
                    return entity;
                }

                // 嘗試通過 PartnerID 屬性找到相關的 Partner
                var partnerIdProperty = entityType.GetProperty("PartnerID");
                if (partnerIdProperty != null)
                {
                    var partnerId = partnerIdProperty.GetValue(entity)?.ToString();
                    if (!string.IsNullOrEmpty(partnerId))
                    {
                        // 嘗試從當前上下文中找到對應的 Partner
                        var partnerEntry = ChangeTracker.Entries()
                            .FirstOrDefault(e => e.Entity.GetType().Name == "Partner" &&
                                          GetEntityId(e.Entity) == partnerId);

                        return partnerEntry?.Entity;
                    }
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary> 獲取 Partner 名稱 </summary>
        private static string? GetPartnerName(object partner)
        {
            try
            {
                var nameProperty = partner.GetType().GetProperty("Name");
                return nameProperty?.GetValue(partner)?.ToString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary> 獲取 Partner 類型 </summary>
        private static string? GetPartnerType(object partner)
        {
            try
            {
                var typeProperty = partner.GetType().GetProperty("Type");
                return typeProperty?.GetValue(partner)?.ToString();
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary> 字典擴展方法 </summary>
    internal static class DictionaryExtensions
    {
        /// <summary> 合併字典 </summary>
        public static void Merge<TKey, TValue>(this Dictionary<TKey, TValue> target, Dictionary<TKey, TValue> source) where TKey : notnull
        {
            foreach (var kvp in source)
            {
                target[kvp.Key] = kvp.Value;
            }
        }
    }

    /// <summary>
    /// 實體變更信息
    /// </summary>
    internal class EntityChangeInfo
    {
        public string EntityType { get; set; } = string.Empty;
        public EntityState State { get; set; }
        public object? Original { get; set; }
        public object Current { get; set; } = null!;
        public string? UserId { get; set; }
    }
}