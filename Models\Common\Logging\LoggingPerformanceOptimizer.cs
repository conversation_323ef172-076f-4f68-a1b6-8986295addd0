using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 日誌記錄效能優化器
    /// 提供快取、限制和效能監控功能，確保大規模實體日誌記錄不影響應用程式效能
    /// </summary>
    public static class LoggingPerformanceOptimizer
    {
        #region 快取管理

        /// <summary> 屬性資訊快取 </summary>
        private static readonly ConcurrentDictionary<Type, CachedTypeInfo> _typeInfoCache = new();

        /// <summary> 快取統計資訊 </summary>
        private static readonly ConcurrentDictionary<string, CacheStatistics> _cacheStats = new();

        /// <summary> 快取清理計時器 </summary>
        private static readonly Timer _cacheCleanupTimer;

        /// <summary> 最大快取項目數 </summary>
        private const int MaxCacheItems = 1000;

        /// <summary> 快取過期時間（分鐘） </summary>
        private const int CacheExpirationMinutes = 30;

        static LoggingPerformanceOptimizer()
        {
            // 每10分鐘清理一次過期快取
            _cacheCleanupTimer = new Timer(CleanupExpiredCache, null, 
                TimeSpan.FromMinutes(10), TimeSpan.FromMinutes(10));
        }

        /// <summary>
        /// 取得快取的類型資訊
        /// </summary>
        /// <param name="type">類型</param>
        /// <returns>快取的類型資訊</returns>
        public static CachedTypeInfo GetCachedTypeInfo(Type type)
        {
            return _typeInfoCache.GetOrAdd(type, t =>
            {
                var stopwatch = Stopwatch.StartNew();
                
                var properties = t.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                    .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                    .ToArray();

                var safeProperties = properties
                    .Where(p => IsSafeType(p.PropertyType))
                    .ToArray();

                var navigationProperties = properties
                    .Where(p => !IsSafeType(p.PropertyType) && !p.PropertyType.IsEnum && !p.PropertyType.IsValueType)
                    .ToArray();

                var primaryKeyProperties = properties
                    .Where(p => p.Name.Equals("Id", StringComparison.OrdinalIgnoreCase) ||
                               p.Name.EndsWith("ID", StringComparison.OrdinalIgnoreCase) ||
                               p.Name.EndsWith("Id", StringComparison.OrdinalIgnoreCase))
                    .ToArray();

                stopwatch.Stop();

                var cachedInfo = new CachedTypeInfo
                {
                    Type = t,
                    AllProperties = properties,
                    SafeProperties = safeProperties,
                    NavigationProperties = navigationProperties,
                    PrimaryKeyProperties = primaryKeyProperties,
                    CreatedAt = DateTime.UtcNow,
                    CacheGenerationTime = stopwatch.ElapsedMilliseconds
                };

                // 更新快取統計
                UpdateCacheStatistics("TypeInfo", true, stopwatch.ElapsedMilliseconds);

                return cachedInfo;
            });
        }

        /// <summary>
        /// 清理過期快取
        /// </summary>
        private static void CleanupExpiredCache(object? state)
        {
            try
            {
                var expiredItems = _typeInfoCache
                    .Where(kvp => DateTime.UtcNow - kvp.Value.CreatedAt > TimeSpan.FromMinutes(CacheExpirationMinutes))
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredItems)
                {
                    _typeInfoCache.TryRemove(key, out _);
                }

                // 如果快取項目過多，移除最少使用的項目
                if (_typeInfoCache.Count > MaxCacheItems)
                {
                    var leastUsedItems = _typeInfoCache
                        .OrderBy(kvp => kvp.Value.AccessCount)
                        .Take(_typeInfoCache.Count - MaxCacheItems)
                        .Select(kvp => kvp.Key)
                        .ToList();

                    foreach (var key in leastUsedItems)
                    {
                        _typeInfoCache.TryRemove(key, out _);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[LoggingPerformanceOptimizer] 快取清理失敗: {ex.Message}");
            }
        }

        #endregion

        #region 效能限制

        /// <summary> 每秒最大日誌記錄數 </summary>
        private const int MaxLogsPerSecond = 100;

        /// <summary> 日誌記錄計數器 </summary>
        private static readonly ConcurrentDictionary<DateTime, int> _logCounters = new();

        /// <summary> 最大屬性數量限制 </summary>
        private const int MaxPropertiesPerEntity = 50;

        /// <summary> 最大字串長度限制 </summary>
        private const int MaxStringLength = 1000;

        /// <summary>
        /// 檢查是否可以記錄日誌（速率限制）
        /// </summary>
        /// <returns>是否可以記錄</returns>
        public static bool CanLog()
        {
            var currentSecond = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, DateTime.UtcNow.Day,
                DateTime.UtcNow.Hour, DateTime.UtcNow.Minute, DateTime.UtcNow.Second);

            var currentCount = _logCounters.AddOrUpdate(currentSecond, 1, (key, value) => value + 1);

            // 清理舊的計數器
            var oldKeys = _logCounters.Keys.Where(k => k < currentSecond.AddSeconds(-10)).ToList();
            foreach (var oldKey in oldKeys)
            {
                _logCounters.TryRemove(oldKey, out _);
            }

            return currentCount <= MaxLogsPerSecond;
        }

        /// <summary>
        /// 限制屬性數量
        /// </summary>
        /// <param name="properties">屬性字典</param>
        /// <returns>限制後的屬性字典</returns>
        public static Dictionary<string, object?> LimitProperties(Dictionary<string, object?> properties)
        {
            if (properties.Count <= MaxPropertiesPerEntity)
                return properties;

            var limitedProperties = properties.Take(MaxPropertiesPerEntity).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            limitedProperties["_truncated"] = $"屬性數量超過限制 ({MaxPropertiesPerEntity})，已截斷";
            
            return limitedProperties;
        }

        /// <summary>
        /// 限制字串長度
        /// </summary>
        /// <param name="value">值</param>
        /// <returns>限制後的值</returns>
        public static object? LimitStringLength(object? value)
        {
            if (value is string str && str.Length > MaxStringLength)
            {
                return str.Substring(0, MaxStringLength) + "...[截斷]";
            }
            return value;
        }

        #endregion

        #region 效能監控

        /// <summary>
        /// 更新快取統計
        /// </summary>
        /// <param name="cacheType">快取類型</param>
        /// <param name="isHit">是否命中</param>
        /// <param name="elapsedMs">耗時（毫秒）</param>
        private static void UpdateCacheStatistics(string cacheType, bool isHit, long elapsedMs)
        {
            _cacheStats.AddOrUpdate(cacheType, 
                new CacheStatistics { HitCount = isHit ? 1 : 0, MissCount = isHit ? 0 : 1, TotalTime = elapsedMs },
                (key, existing) => new CacheStatistics
                {
                    HitCount = existing.HitCount + (isHit ? 1 : 0),
                    MissCount = existing.MissCount + (isHit ? 0 : 1),
                    TotalTime = existing.TotalTime + elapsedMs
                });
        }

        /// <summary>
        /// 取得效能統計資訊
        /// </summary>
        /// <returns>效能統計</returns>
        public static Dictionary<string, object> GetPerformanceStatistics()
        {
            var stats = new Dictionary<string, object>
            {
                ["cacheSize"] = _typeInfoCache.Count,
                ["maxCacheItems"] = MaxCacheItems,
                ["cacheExpirationMinutes"] = CacheExpirationMinutes,
                ["maxLogsPerSecond"] = MaxLogsPerSecond,
                ["maxPropertiesPerEntity"] = MaxPropertiesPerEntity,
                ["maxStringLength"] = MaxStringLength,
                ["cacheStatistics"] = _cacheStats.ToDictionary(kvp => kvp.Key, kvp => new
                {
                    hitCount = kvp.Value.HitCount,
                    missCount = kvp.Value.MissCount,
                    hitRate = kvp.Value.HitCount + kvp.Value.MissCount > 0 
                        ? (double)kvp.Value.HitCount / (kvp.Value.HitCount + kvp.Value.MissCount) * 100 
                        : 0,
                    averageTime = kvp.Value.HitCount + kvp.Value.MissCount > 0 
                        ? (double)kvp.Value.TotalTime / (kvp.Value.HitCount + kvp.Value.MissCount) 
                        : 0
                })
            };

            return stats;
        }

        #endregion

        #region 輔助方法

        /// <summary>
        /// 檢查是否為安全類型
        /// </summary>
        /// <param name="type">類型</param>
        /// <returns>是否為安全類型</returns>
        private static bool IsSafeType(Type type)
        {
            var safeTypes = new HashSet<Type>
            {
                typeof(string), typeof(int), typeof(long), typeof(decimal), typeof(double), typeof(float),
                typeof(DateTime), typeof(DateTimeOffset), typeof(Guid), typeof(bool), typeof(byte), typeof(short),
                typeof(uint), typeof(ulong), typeof(ushort), typeof(sbyte), typeof(char),
                // 可空版本
                typeof(int?), typeof(long?), typeof(decimal?), typeof(double?), typeof(float?),
                typeof(DateTime?), typeof(DateTimeOffset?), typeof(Guid?), typeof(bool?), typeof(byte?), typeof(short?),
                typeof(uint?), typeof(ulong?), typeof(ushort?), typeof(sbyte?), typeof(char?)
            };

            return safeTypes.Contains(type) || safeTypes.Contains(Nullable.GetUnderlyingType(type));
        }

        #endregion
    }

    /// <summary>
    /// 快取的類型資訊
    /// </summary>
    public class CachedTypeInfo
    {
        public Type Type { get; set; } = null!;
        public PropertyInfo[] AllProperties { get; set; } = Array.Empty<PropertyInfo>();
        public PropertyInfo[] SafeProperties { get; set; } = Array.Empty<PropertyInfo>();
        public PropertyInfo[] NavigationProperties { get; set; } = Array.Empty<PropertyInfo>();
        public PropertyInfo[] PrimaryKeyProperties { get; set; } = Array.Empty<PropertyInfo>();
        public DateTime CreatedAt { get; set; }
        public long CacheGenerationTime { get; set; }

        /// <summary> 存取計數（使用欄位以支援 Interlocked 操作） </summary>
        private long _accessCount;

        /// <summary> 存取計數屬性 </summary>
        public long AccessCount => _accessCount;

        /// <summary>
        /// 增加存取計數
        /// </summary>
        public void IncrementAccess()
        {
            Interlocked.Increment(ref _accessCount);
        }
    }

    /// <summary>
    /// 快取統計資訊
    /// </summary>
    public class CacheStatistics
    {
        public long HitCount { get; set; }
        public long MissCount { get; set; }
        public long TotalTime { get; set; }
    }
}
