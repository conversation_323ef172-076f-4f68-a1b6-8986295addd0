using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.EntityFrameworkCore;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 循環引用防護器
    /// 提供系統性的循環引用檢測和防護機制，確保複雜實體層次結構的安全序列化
    /// </summary>
    public class CircularReferenceGuard
    {
        #region 私有欄位

        /// <summary> 已訪問的物件追蹤 </summary>
        private readonly HashSet<object> _visitedObjects;

        /// <summary> 物件路徑追蹤 </summary>
        private readonly Stack<string> _objectPath;

        /// <summary> 深度限制 </summary>
        private readonly int _maxDepth;

        /// <summary> 當前深度 </summary>
        private int _currentDepth;

        /// <summary> 屬性數量限制 </summary>
        private readonly int _maxProperties;

        /// <summary> 已處理屬性計數 </summary>
        private int _processedProperties;

        #endregion

        #region 靜態快取

        /// <summary> 導航屬性快取 </summary>
        private static readonly ConcurrentDictionary<Type, HashSet<string>> _navigationPropertyCache = new();

        /// <summary> 安全屬性類型快取 </summary>
        private static readonly HashSet<Type> _safeTypes = new()
        {
            typeof(string), typeof(int), typeof(long), typeof(decimal), typeof(double), typeof(float),
            typeof(DateTime), typeof(DateTimeOffset), typeof(Guid), typeof(bool), typeof(byte), typeof(short),
            typeof(uint), typeof(ulong), typeof(ushort), typeof(sbyte), typeof(char),
            // 可空版本
            typeof(int?), typeof(long?), typeof(decimal?), typeof(double?), typeof(float?),
            typeof(DateTime?), typeof(DateTimeOffset?), typeof(Guid?), typeof(bool?), typeof(byte?), typeof(short?),
            typeof(uint?), typeof(ulong?), typeof(ushort?), typeof(sbyte?), typeof(char?)
        };

        #endregion

        #region 建構子

        /// <summary>
        /// 初始化循環引用防護器
        /// </summary>
        /// <param name="maxDepth">最大深度限制</param>
        /// <param name="maxProperties">最大屬性數量限制</param>
        public CircularReferenceGuard(int maxDepth = 5, int maxProperties = 100)
        {
            _visitedObjects = new HashSet<object>(ReferenceEqualityComparer.Instance);
            _objectPath = new Stack<string>();
            _maxDepth = maxDepth;
            _maxProperties = maxProperties;
            _currentDepth = 0;
            _processedProperties = 0;
        }

        #endregion

        #region 公開方法

        /// <summary>
        /// 安全提取物件屬性，防止循環引用
        /// </summary>
        /// <param name="obj">要提取的物件</param>
        /// <param name="objectName">物件名稱（用於路徑追蹤）</param>
        /// <returns>安全的屬性字典</returns>
        public Dictionary<string, object?> SafeExtractProperties(object? obj, string objectName = "root")
        {
            if (obj == null)
                return new Dictionary<string, object?>();

            // 檢查深度限制
            if (_currentDepth >= _maxDepth)
            {
                return new Dictionary<string, object?>
                {
                    ["_warning"] = $"達到最大深度限制 ({_maxDepth})，停止提取"
                };
            }

            // 檢查屬性數量限制
            if (_processedProperties >= _maxProperties)
            {
                return new Dictionary<string, object?>
                {
                    ["_warning"] = $"達到最大屬性數量限制 ({_maxProperties})，停止提取"
                };
            }

            // 檢查循環引用
            if (_visitedObjects.Contains(obj))
            {
                return new Dictionary<string, object?>
                {
                    ["_circularReference"] = $"循環引用檢測到，路徑: {string.Join(" -> ", _objectPath.Reverse())} -> {objectName}"
                };
            }

            try
            {
                _visitedObjects.Add(obj);
                _objectPath.Push(objectName);
                _currentDepth++;

                return ExtractPropertiesInternal(obj);
            }
            finally
            {
                _visitedObjects.Remove(obj);
                _objectPath.Pop();
                _currentDepth--;
            }
        }

        /// <summary>
        /// 檢查物件是否安全（無循環引用）
        /// </summary>
        /// <param name="obj">要檢查的物件</param>
        /// <param name="objectName">物件名稱</param>
        /// <returns>是否安全</returns>
        public bool IsSafe(object? obj, string objectName = "root")
        {
            if (obj == null) return true;
            if (_currentDepth >= _maxDepth) return false;
            if (_processedProperties >= _maxProperties) return false;
            if (_visitedObjects.Contains(obj)) return false;

            return true;
        }

        /// <summary>
        /// 取得當前訪問路徑
        /// </summary>
        /// <returns>訪問路徑字串</returns>
        public string GetCurrentPath()
        {
            return string.Join(" -> ", _objectPath.Reverse());
        }

        /// <summary>
        /// 重置防護器狀態
        /// </summary>
        public void Reset()
        {
            _visitedObjects.Clear();
            _objectPath.Clear();
            _currentDepth = 0;
            _processedProperties = 0;
        }

        #endregion

        #region 私有方法

        /// <summary> 內部屬性提取邏輯 </summary>
        private Dictionary<string, object?> ExtractPropertiesInternal(object obj)
        {
            var result = new Dictionary<string, object?>();
            var type = obj.GetType();

            // 取得導航屬性清單
            var navigationProperties = GetNavigationProperties(type);

            // 取得所有可讀屬性
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                .ToArray();

            foreach (var property in properties)
            {
                // 檢查屬性數量限制
                if (_processedProperties >= _maxProperties)
                {
                    result["_truncated"] = $"屬性數量超過限制 ({_maxProperties})，後續屬性被截斷";
                    break;
                }

                try
                {
                    var value = property.GetValue(obj);
                    _processedProperties++;

                    // 處理不同類型的屬性
                    if (value == null)
                    {
                        result[property.Name] = null;
                    }
                    else if (IsSafeType(property.PropertyType))
                    {
                        // 安全類型直接記錄
                        result[property.Name] = FormatValue(value);
                    }
                    else if (property.PropertyType.IsEnum)
                    {
                        // 列舉類型
                        result[property.Name] = value.ToString();
                    }
                    else if (navigationProperties.Contains(property.Name))
                    {
                        // 導航屬性 - 只記錄基本資訊，避免深度遞迴
                        result[property.Name] = CreateNavigationPropertySummary(value, property.Name);
                    }
                    else if (IsCollectionType(property.PropertyType))
                    {
                        // 集合類型 - 限制數量
                        result[property.Name] = ProcessCollection(value, property.Name);
                    }
                    else
                    {
                        // 複雜物件 - 遞迴處理（有深度限制）
                        result[property.Name] = SafeExtractProperties(value, property.Name);
                    }
                }
                catch (Exception ex)
                {
                    result[property.Name] = $"[屬性讀取錯誤: {ex.Message}]";
                }
            }

            return result;
        }

        /// <summary> 取得導航屬性清單 </summary>
        private static HashSet<string> GetNavigationProperties(Type type)
        {
            return _navigationPropertyCache.GetOrAdd(type, t =>
            {
                var navigationProps = new HashSet<string>();

                // 基於命名約定識別導航屬性
                var properties = t.GetProperties(BindingFlags.Public | BindingFlags.Instance);
                
                foreach (var prop in properties)
                {
                    // 如果屬性類型是複雜類型且不是安全類型，可能是導航屬性
                    if (!IsSafeType(prop.PropertyType) && 
                        !prop.PropertyType.IsEnum && 
                        !prop.PropertyType.IsValueType &&
                        prop.PropertyType != typeof(string))
                    {
                        // 進一步檢查是否為實體類型
                        if (IsLikelyEntityType(prop.PropertyType))
                        {
                            navigationProps.Add(prop.Name);
                        }
                    }
                }

                return navigationProps;
            });
        }

        /// <summary> 檢查是否為安全類型 </summary>
        private static bool IsSafeType(Type type)
        {
            return _safeTypes.Contains(type) || 
                   _safeTypes.Contains(Nullable.GetUnderlyingType(type));
        }

        /// <summary> 檢查是否為集合類型 </summary>
        private static bool IsCollectionType(Type type)
        {
            return type != typeof(string) && 
                   (type.IsArray || 
                    type.GetInterfaces().Any(i => 
                        i.IsGenericType && 
                        i.GetGenericTypeDefinition() == typeof(IEnumerable<>)));
        }

        /// <summary> 檢查是否可能是實體類型 </summary>
        private static bool IsLikelyEntityType(Type type)
        {
            // 簡單的啟發式檢查
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            
            // 如果有 ID 屬性，可能是實體
            return properties.Any(p => 
                p.Name.Equals("Id", StringComparison.OrdinalIgnoreCase) ||
                p.Name.EndsWith("ID", StringComparison.OrdinalIgnoreCase) ||
                p.Name.EndsWith("Id", StringComparison.OrdinalIgnoreCase));
        }

        /// <summary> 建立導航屬性摘要 </summary>
        private Dictionary<string, object?> CreateNavigationPropertySummary(object value, string propertyName)
        {
            var summary = new Dictionary<string, object?>
            {
                ["_type"] = value.GetType().Name,
                ["_isNavigationProperty"] = true
            };

            try
            {
                // 嘗試取得 ID 屬性
                var idProperty = value.GetType().GetProperties()
                    .FirstOrDefault(p => 
                        p.Name.Equals("Id", StringComparison.OrdinalIgnoreCase) ||
                        p.Name.EndsWith("ID", StringComparison.OrdinalIgnoreCase) ||
                        p.Name.EndsWith("Id", StringComparison.OrdinalIgnoreCase));

                if (idProperty != null)
                {
                    var idValue = idProperty.GetValue(value);
                    summary["_id"] = FormatValue(idValue);
                }
            }
            catch
            {
                summary["_id"] = "[無法取得]";
            }

            return summary;
        }

        /// <summary> 處理集合 </summary>
        private object ProcessCollection(object collection, string propertyName)
        {
            try
            {
                if (collection is System.Collections.IEnumerable enumerable)
                {
                    var items = new List<object?>();
                    int count = 0;
                    const int maxItems = 3; // 限制集合項目數量

                    foreach (var item in enumerable)
                    {
                        if (count >= maxItems)
                        {
                            items.Add($"[還有更多項目，已截斷於 {maxItems} 項]");
                            break;
                        }

                        if (item != null && IsSafeType(item.GetType()))
                        {
                            items.Add(FormatValue(item));
                        }
                        else
                        {
                            items.Add(CreateNavigationPropertySummary(item!, $"{propertyName}[{count}]"));
                        }

                        count++;
                    }

                    return new Dictionary<string, object?>
                    {
                        ["_type"] = "Collection",
                        ["_count"] = count,
                        ["_items"] = items
                    };
                }
            }
            catch (Exception ex)
            {
                return $"[集合處理錯誤: {ex.Message}]";
            }

            return "[無法處理的集合類型]";
        }

        /// <summary> 格式化值 </summary>
        private static object? FormatValue(object? value)
        {
            return value switch
            {
                null => null,
                Guid guid => guid.ToString(),
                DateTime dateTime => dateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("yyyy-MM-dd HH:mm:ss"),
                decimal dec => dec.ToString("F2"),
                _ => value
            };
        }

        #endregion
    }
}
