using Microsoft.AspNetCore.Mvc;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Common.Logging;
using FAST_ERP_Backend.Interfaces.Common;
using System.Threading.Tasks;

namespace FAST_ERP_Backend.Controllers.Common
{
    /// <summary>
    /// 日誌記錄系統驗證控制器
    /// 提供 API 端點來驗證新的通用日誌記錄系統功能
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class LoggingValidationController : ControllerBase
    {
        private readonly ERPDbContext _context;
        private readonly ILoggerService _logger;

        public LoggingValidationController(ERPDbContext context, ILoggerService logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 驗證循環引用防護功能
        /// </summary>
        /// <returns>驗證結果</returns>
        [HttpGet("circular-reference-guard")]
        public IActionResult ValidateCircularReferenceGuard()
        {
            try
            {
                var result = LoggingSystemValidator.ValidateCircularReferenceGuard();
                return Ok(new
                {
                    success = result.IsSuccess,
                    message = result.Message,
                    elapsedMs = result.ElapsedMilliseconds,
                    details = result.Details
                });
            }
            catch (System.Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 驗證通用實體變更追蹤功能
        /// </summary>
        /// <returns>驗證結果</returns>
        [HttpGet("entity-change-tracker")]
        public IActionResult ValidateEntityChangeTracker()
        {
            try
            {
                var result = LoggingSystemValidator.ValidateGenericEntityChangeTracker(_context);
                return Ok(new
                {
                    success = result.IsSuccess,
                    message = result.Message,
                    elapsedMs = result.ElapsedMilliseconds,
                    details = result.Details
                });
            }
            catch (System.Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 驗證簡化日誌轉換功能
        /// </summary>
        /// <returns>驗證結果</returns>
        [HttpGet("simplified-log-converter")]
        public IActionResult ValidateSimplifiedLogConverter()
        {
            try
            {
                var result = LoggingSystemValidator.ValidateSimplifiedLogConverter();
                return Ok(new
                {
                    success = result.IsSuccess,
                    message = result.Message,
                    elapsedMs = result.ElapsedMilliseconds,
                    details = result.Details
                });
            }
            catch (System.Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 驗證效能優化器功能
        /// </summary>
        /// <returns>驗證結果</returns>
        [HttpGet("performance-optimizer")]
        public IActionResult ValidatePerformanceOptimizer()
        {
            try
            {
                var result = LoggingSystemValidator.ValidatePerformanceOptimizer();
                return Ok(new
                {
                    success = result.IsSuccess,
                    message = result.Message,
                    elapsedMs = result.ElapsedMilliseconds,
                    details = result.Details
                });
            }
            catch (System.Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 驗證安全序列化器功能
        /// </summary>
        /// <returns>驗證結果</returns>
        [HttpGet("safe-bson-serializer")]
        public IActionResult ValidateSafeBsonSerializer()
        {
            try
            {
                var result = LoggingSystemValidator.ValidateSafeBsonSerializer();
                return Ok(new
                {
                    success = result.IsSuccess,
                    message = result.Message,
                    elapsedMs = result.ElapsedMilliseconds,
                    details = result.Details
                });
            }
            catch (System.Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 執行完整的系統驗證
        /// </summary>
        /// <returns>完整驗證結果</returns>
        [HttpGet("complete-validation")]
        public async Task<IActionResult> ValidateCompleteSystem()
        {
            try
            {
                var result = await LoggingSystemValidator.ValidateCompleteSystem(_context);
                return Ok(new
                {
                    success = result.IsSuccess,
                    summary = result.Summary,
                    totalTests = result.TotalTests,
                    successfulTests = result.SuccessfulTests,
                    failedTests = result.FailedTests,
                    totalElapsedMs = result.TotalElapsedMilliseconds,
                    results = result.Results.Select(r => new
                    {
                        success = r.IsSuccess,
                        message = r.Message,
                        elapsedMs = r.ElapsedMilliseconds,
                        details = r.Details
                    })
                });
            }
            catch (System.Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 取得效能統計資訊
        /// </summary>
        /// <returns>效能統計</returns>
        [HttpGet("performance-statistics")]
        public IActionResult GetPerformanceStatistics()
        {
            try
            {
                var stats = LoggingPerformanceOptimizer.GetPerformanceStatistics();
                return Ok(stats);
            }
            catch (System.Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 測試實際的實體變更日誌記錄
        /// </summary>
        /// <returns>測試結果</returns>
        [HttpPost("test-entity-logging")]
        public async Task<IActionResult> TestEntityLogging()
        {
            try
            {
                // 建立測試實體
                var partner = new Models.Ims.Partner
                {
                    PartnerID = System.Guid.NewGuid(),
                    IsStop = false
                };

                // 記錄建立操作
                _context.Ims_Partner.Add(partner);
                await _context.SaveChangesAsync();

                // 記錄更新操作
                partner.IsStop = true;
                await _context.SaveChangesAsync();

                // 記錄刪除操作
                _context.Ims_Partner.Remove(partner);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "實體變更日誌記錄測試完成",
                    partnerID = partner.PartnerID.ToString(),
                    operations = new[] { "CREATE", "UPDATE", "DELETE" }
                });
            }
            catch (System.Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// 測試複雜實體層次結構的日誌記錄
        /// </summary>
        /// <returns>測試結果</returns>
        [HttpPost("test-complex-entity-logging")]
        public async Task<IActionResult> TestComplexEntityLogging()
        {
            try
            {
                // 建立複雜的實體層次結構
                var partner = new Models.Ims.Partner
                {
                    PartnerID = System.Guid.NewGuid(),
                    IsStop = false,
                    IndividualDetail = new Models.Ims.IndividualDetail
                    {
                        PartnerID = System.Guid.NewGuid(),
                        FirstName = "測試",
                        LastName = "用戶"
                    }
                };

                // 記錄複雜實體的建立
                _context.Ims_Partner.Add(partner);
                await _context.SaveChangesAsync();

                // 更新嵌套實體
                if (partner.IndividualDetail != null)
                {
                    partner.IndividualDetail.FirstName = "更新後的測試";
                    await _context.SaveChangesAsync();
                }

                // 清理測試資料
                _context.Ims_Partner.Remove(partner);
                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = "複雜實體層次結構日誌記錄測試完成",
                    partnerID = partner.PartnerID.ToString(),
                    individualDetailID = partner.IndividualDetail?.PartnerID.ToString(),
                    operations = new[] { "CREATE_COMPLEX", "UPDATE_NESTED", "DELETE_CASCADE" }
                });
            }
            catch (System.Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
