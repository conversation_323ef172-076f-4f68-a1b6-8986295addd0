using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 通用實體變更追蹤器
    /// 提供完全通用的實體變更追蹤，無需針對特定模型進行修改
    /// </summary>
    public static class GenericEntityChangeTracker
    {
        #region 快取和設定

        /// <summary> 實體類型元數據快取 </summary>
        private static readonly ConcurrentDictionary<Type, EntityTypeMetadata> _entityMetadataCache = new();

        /// <summary> 屬性資訊快取 </summary>
        private static readonly ConcurrentDictionary<Type, PropertyInfo[]> _propertyCache = new();

        /// <summary> 主鍵屬性快取 </summary>
        private static readonly ConcurrentDictionary<Type, PropertyInfo[]> _primaryKeyCache = new();

        /// <summary> 導航屬性快取 </summary>
        private static readonly ConcurrentDictionary<Type, PropertyInfo[]> _navigationPropertyCache = new();

        /// <summary> 安全屬性類型 </summary>
        private static readonly HashSet<Type> _safeTypes = new()
        {
            typeof(string), typeof(int), typeof(long), typeof(decimal), typeof(double), typeof(float),
            typeof(DateTime), typeof(DateTimeOffset), typeof(Guid), typeof(bool), typeof(byte), typeof(short),
            typeof(uint), typeof(ulong), typeof(ushort), typeof(sbyte), typeof(char),
            // 可空版本
            typeof(int?), typeof(long?), typeof(decimal?), typeof(double?), typeof(float?),
            typeof(DateTime?), typeof(DateTimeOffset?), typeof(Guid?), typeof(bool?), typeof(byte?), typeof(short?),
            typeof(uint?), typeof(ulong?), typeof(ushort?), typeof(sbyte?), typeof(char?)
        };

        #endregion

        #region 公開方法

        /// <summary>
        /// 從 EntityEntry 提取變更資訊
        /// </summary>
        /// <param name="entry">實體項目</param>
        /// <param name="context">資料庫上下文</param>
        /// <returns>實體日誌記錄 DTO</returns>
        public static EntityLoggingDTO ExtractEntityChange(EntityEntry entry, DbContext context)
        {
            var entityType = entry.Entity.GetType();
            var metadata = GetEntityMetadata(entityType, context);

            var dto = new EntityLoggingDTO
            {
                EntityType = entityType.Name,
                EntityId = ExtractEntityId(entry.Entity, metadata),
                EntityState = entry.State.ToString(),
                Timestamp = DateTime.UtcNow,
                UserId = ExtractUserId(entry.Entity),
                EntityPath = GetEntityPath(entry.Entity, context)
            };

            // 提取屬性資料
            switch (entry.State)
            {
                case EntityState.Added:
                    dto.Properties = ExtractSafeProperties(entry.Entity, metadata);
                    break;

                case EntityState.Modified:
                    dto.Properties = ExtractSafeProperties(entry.Entity, metadata);
                    dto.OriginalProperties = ExtractOriginalProperties(entry, metadata);
                    dto.ChangedProperties = GetChangedProperties(entry, metadata);
                    break;

                case EntityState.Deleted:
                    dto.Properties = ExtractSafeProperties(entry.Entity, metadata);
                    break;
            }

            // 提取相關實體資訊
            dto.RelatedEntities = ExtractRelatedEntities(entry.Entity, metadata, context);
            dto.ParentEntity = ExtractParentEntity(entry.Entity, metadata, context);

            return dto;
        }

        /// <summary>
        /// 批量處理實體變更
        /// </summary>
        /// <param name="entries">實體項目清單</param>
        /// <param name="context">資料庫上下文</param>
        /// <returns>實體變更日誌 DTO</returns>
        public static EntityChangeLogDTO ProcessEntityChanges(IEnumerable<EntityEntry> entries, DbContext context)
        {
            var changedEntries = entries.Where(e => e.State != EntityState.Unchanged).ToList();
            
            var changeLog = new EntityChangeLogDTO
            {
                TransactionId = Guid.NewGuid().ToString(),
                Source = context.GetType().Name,
                ChangeTime = DateTime.UtcNow,
                TotalChanges = changedEntries.Count
            };

            foreach (var entry in changedEntries)
            {
                try
                {
                    var entityDto = ExtractEntityChange(entry, context);
                    changeLog.ChangedEntities.Add(entityDto);
                }
                catch (Exception ex)
                {
                    // 如果單個實體處理失敗，記錄錯誤但不中斷整個流程
                    var errorDto = new EntityLoggingDTO
                    {
                        EntityType = entry.Entity.GetType().Name,
                        EntityId = "ERROR",
                        EntityState = entry.State.ToString(),
                        Timestamp = DateTime.UtcNow,
                        Properties = new Dictionary<string, object?>
                        {
                            ["error"] = ex.Message,
                            ["errorType"] = ex.GetType().Name
                        }
                    };
                    changeLog.ChangedEntities.Add(errorDto);
                }
            }

            return changeLog;
        }

        #endregion

        #region 私有方法

        /// <summary> 取得實體類型元數據 </summary>
        private static EntityTypeMetadata GetEntityMetadata(Type entityType, DbContext context)
        {
            return _entityMetadataCache.GetOrAdd(entityType, type =>
            {
                // 使用效能優化器的快取
                var cachedTypeInfo = LoggingPerformanceOptimizer.GetCachedTypeInfo(type);
                cachedTypeInfo.IncrementAccess();

                var entityTypeInfo = context.Model.FindEntityType(type);

                return new EntityTypeMetadata
                {
                    EntityType = type,
                    Properties = cachedTypeInfo.AllProperties,
                    PrimaryKeys = cachedTypeInfo.PrimaryKeyProperties,
                    NavigationProperties = cachedTypeInfo.NavigationProperties,
                    EntityTypeInfo = entityTypeInfo
                };
            });
        }

        /// <summary> 取得快取的屬性資訊 </summary>
        private static PropertyInfo[] GetCachedProperties(Type type)
        {
            return _propertyCache.GetOrAdd(type, t =>
                t.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                 .Where(p => p.CanRead && p.GetIndexParameters().Length == 0)
                 .ToArray());
        }

        /// <summary> 取得快取的主鍵屬性 </summary>
        private static PropertyInfo[] GetCachedPrimaryKeys(Type type, IEntityType? entityTypeInfo)
        {
            return _primaryKeyCache.GetOrAdd(type, t =>
            {
                if (entityTypeInfo?.FindPrimaryKey() != null)
                {
                    return entityTypeInfo.FindPrimaryKey()!.Properties
                        .Select(p => t.GetProperty(p.Name))
                        .Where(p => p != null)
                        .ToArray()!;
                }

                // 備用方案：尋找名為 "Id" 或以 "ID" 結尾的屬性
                var properties = GetCachedProperties(t);
                return properties.Where(p => 
                    p.Name.Equals("Id", StringComparison.OrdinalIgnoreCase) ||
                    p.Name.EndsWith("ID", StringComparison.OrdinalIgnoreCase) ||
                    p.Name.EndsWith("Id", StringComparison.OrdinalIgnoreCase))
                    .ToArray();
            });
        }

        /// <summary> 取得快取的導航屬性 </summary>
        private static PropertyInfo[] GetCachedNavigationProperties(Type type, IEntityType? entityTypeInfo)
        {
            return _navigationPropertyCache.GetOrAdd(type, t =>
            {
                if (entityTypeInfo != null)
                {
                    var navigationNames = entityTypeInfo.GetNavigations()
                        .Select(n => n.Name)
                        .ToHashSet();

                    return GetCachedProperties(t)
                        .Where(p => navigationNames.Contains(p.Name))
                        .ToArray();
                }

                // 備用方案：根據類型判斷導航屬性
                return GetCachedProperties(t)
                    .Where(p => !_safeTypes.Contains(p.PropertyType) && 
                               !p.PropertyType.IsEnum &&
                               !p.PropertyType.IsValueType)
                    .ToArray();
            });
        }

        /// <summary> 提取實體ID </summary>
        private static string ExtractEntityId(object entity, EntityTypeMetadata metadata)
        {
            try
            {
                var primaryKeys = metadata.PrimaryKeys;
                if (primaryKeys.Length == 1)
                {
                    var value = primaryKeys[0].GetValue(entity);
                    return value?.ToString() ?? "NULL";
                }
                else if (primaryKeys.Length > 1)
                {
                    // 複合主鍵
                    var keyValues = primaryKeys.Select(pk => pk.GetValue(entity)?.ToString() ?? "NULL");
                    return string.Join("|", keyValues);
                }

                return "UNKNOWN";
            }
            catch
            {
                return "ERROR";
            }
        }

        /// <summary> 提取使用者ID </summary>
        private static string? ExtractUserId(object entity)
        {
            try
            {
                var userIdProperty = entity.GetType()
                    .GetProperty("UpdateUserId") ?? 
                    entity.GetType().GetProperty("CreateUserId");

                return userIdProperty?.GetValue(entity)?.ToString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary> 取得實體路徑 </summary>
        private static string? GetEntityPath(object entity, DbContext context)
        {
            // 這裡可以實現更複雜的路徑追蹤邏輯
            // 目前簡單返回實體類型名稱
            return entity.GetType().Name;
        }

        /// <summary> 提取安全屬性 </summary>
        private static Dictionary<string, object?> ExtractSafeProperties(object entity, EntityTypeMetadata metadata)
        {
            var result = new Dictionary<string, object?>();

            foreach (var property in metadata.Properties)
            {
                try
                {
                    // 跳過導航屬性
                    if (metadata.NavigationProperties.Contains(property))
                        continue;

                    // 只處理安全類型
                    if (!_safeTypes.Contains(property.PropertyType) && !property.PropertyType.IsEnum)
                        continue;

                    var value = property.GetValue(entity);
                    var formattedValue = FormatValue(value);

                    // 使用效能優化器限制字串長度
                    result[property.Name] = LoggingPerformanceOptimizer.LimitStringLength(formattedValue);
                }
                catch
                {
                    result[property.Name] = "[讀取失敗]";
                }
            }

            // 使用效能優化器限制屬性數量
            return LoggingPerformanceOptimizer.LimitProperties(result);
        }

        /// <summary> 提取原始屬性值 </summary>
        private static Dictionary<string, object?> ExtractOriginalProperties(EntityEntry entry, EntityTypeMetadata metadata)
        {
            var result = new Dictionary<string, object?>();

            foreach (var property in metadata.Properties)
            {
                try
                {
                    // 跳過導航屬性
                    if (metadata.NavigationProperties.Contains(property))
                        continue;

                    // 只處理安全類型
                    if (!_safeTypes.Contains(property.PropertyType) && !property.PropertyType.IsEnum)
                        continue;

                    var originalValue = entry.OriginalValues[property.Name];
                    result[property.Name] = FormatValue(originalValue);
                }
                catch
                {
                    result[property.Name] = "[讀取失敗]";
                }
            }

            return result;
        }

        /// <summary> 取得變更的屬性清單 </summary>
        private static List<string> GetChangedProperties(EntityEntry entry, EntityTypeMetadata metadata)
        {
            var changedProperties = new List<string>();

            foreach (var property in metadata.Properties)
            {
                try
                {
                    // 跳過導航屬性
                    if (metadata.NavigationProperties.Contains(property))
                        continue;

                    if (entry.Property(property.Name).IsModified)
                    {
                        changedProperties.Add(property.Name);
                    }
                }
                catch
                {
                    // 忽略無法檢查的屬性
                }
            }

            return changedProperties;
        }

        /// <summary> 提取相關實體資訊 </summary>
        private static Dictionary<string, object?>? ExtractRelatedEntities(object entity, EntityTypeMetadata metadata, DbContext context)
        {
            // 這裡可以實現相關實體的提取邏輯
            // 目前返回 null，避免循環引用
            return null;
        }

        /// <summary> 提取父實體資訊 </summary>
        private static EntityReference? ExtractParentEntity(object entity, EntityTypeMetadata metadata, DbContext context)
        {
            // 這裡可以實現父實體的識別邏輯
            // 目前返回 null
            return null;
        }

        /// <summary> 格式化值 </summary>
        private static object? FormatValue(object? value)
        {
            return value switch
            {
                null => null,
                Guid guid => guid.ToString(),
                DateTime dateTime => dateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("yyyy-MM-dd HH:mm:ss"),
                decimal dec => dec.ToString("F2"),
                _ => value
            };
        }

        #endregion
    }

    /// <summary>
    /// 實體類型元數據
    /// </summary>
    internal class EntityTypeMetadata
    {
        public Type EntityType { get; set; } = null!;
        public PropertyInfo[] Properties { get; set; } = Array.Empty<PropertyInfo>();
        public PropertyInfo[] PrimaryKeys { get; set; } = Array.Empty<PropertyInfo>();
        public PropertyInfo[] NavigationProperties { get; set; } = Array.Empty<PropertyInfo>();
        public IEntityType? EntityTypeInfo { get; set; }
    }
}
