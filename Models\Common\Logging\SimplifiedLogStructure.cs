using System;
using System.Collections.Generic;
using System.Linq;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 簡化的日誌結構
    /// 提供清晰、易讀的日誌格式，消除複雜的嵌套結構
    /// </summary>
    public class SimplifiedLogEntry
    {
        /// <summary> 日誌ID </summary>
        public string LogId { get; set; } = Guid.NewGuid().ToString();

        /// <summary> 交易ID </summary>
        public string TransactionId { get; set; } = string.Empty;

        /// <summary> 時間戳記 </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary> 操作來源 </summary>
        public string Source { get; set; } = "System";

        /// <summary> 操作類型 (CREATE, UPDATE, DELETE) </summary>
        public string Operation { get; set; } = string.Empty;

        /// <summary> 實體類型 </summary>
        public string EntityType { get; set; } = string.Empty;

        /// <summary> 實體ID </summary>
        public string EntityId { get; set; } = string.Empty;

        /// <summary> 使用者ID </summary>
        public string? UserId { get; set; }

        /// <summary> 變更摘要 </summary>
        public string Summary { get; set; } = string.Empty;

        /// <summary> 變更詳細資料 </summary>
        public SimplifiedChangeData? Changes { get; set; }

        /// <summary> 相關實體 </summary>
        public List<RelatedEntityInfo>? RelatedEntities { get; set; }

        /// <summary> 額外資訊 </summary>
        public Dictionary<string, string>? Metadata { get; set; }
    }

    /// <summary>
    /// 簡化的變更資料結構
    /// </summary>
    public class SimplifiedChangeData
    {
        /// <summary> 新增的屬性 (僅用於 CREATE 操作) </summary>
        public Dictionary<string, object?>? Added { get; set; }

        /// <summary> 修改的屬性 (僅用於 UPDATE 操作) </summary>
        public Dictionary<string, PropertyChange>? Modified { get; set; }

        /// <summary> 刪除的屬性 (僅用於 DELETE 操作) </summary>
        public Dictionary<string, object?>? Deleted { get; set; }

        /// <summary> 變更屬性數量 </summary>
        public int ChangeCount => 
            (Added?.Count ?? 0) + 
            (Modified?.Count ?? 0) + 
            (Deleted?.Count ?? 0);
    }

    /// <summary>
    /// 屬性變更資訊
    /// </summary>
    public class PropertyChange
    {
        /// <summary> 變更前的值 </summary>
        public object? From { get; set; }

        /// <summary> 變更後的值 </summary>
        public object? To { get; set; }

        /// <summary> 變更類型 </summary>
        public string ChangeType { get; set; } = "Modified";

        /// <summary> 是否為重要變更 </summary>
        public bool IsSignificant { get; set; } = true;
    }

    /// <summary>
    /// 相關實體資訊
    /// </summary>
    public class RelatedEntityInfo
    {
        /// <summary> 實體類型 </summary>
        public string EntityType { get; set; } = string.Empty;

        /// <summary> 實體ID </summary>
        public string EntityId { get; set; } = string.Empty;

        /// <summary> 關聯類型 </summary>
        public string RelationType { get; set; } = string.Empty;

        /// <summary> 關聯描述 </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 簡化日誌建構器
    /// 提供流暢的API來建立簡化的日誌項目
    /// </summary>
    public class SimplifiedLogBuilder
    {
        private readonly SimplifiedLogEntry _logEntry;

        public SimplifiedLogBuilder()
        {
            _logEntry = new SimplifiedLogEntry();
        }

        public SimplifiedLogBuilder WithTransactionId(string transactionId)
        {
            _logEntry.TransactionId = transactionId;
            return this;
        }

        public SimplifiedLogBuilder WithSource(string source)
        {
            _logEntry.Source = source;
            return this;
        }

        public SimplifiedLogBuilder WithOperation(string operation)
        {
            _logEntry.Operation = operation.ToUpperInvariant();
            return this;
        }

        public SimplifiedLogBuilder WithEntity(string entityType, string entityId)
        {
            _logEntry.EntityType = entityType;
            _logEntry.EntityId = entityId;
            return this;
        }

        public SimplifiedLogBuilder WithUser(string? userId)
        {
            _logEntry.UserId = userId;
            return this;
        }

        public SimplifiedLogBuilder WithSummary(string summary)
        {
            _logEntry.Summary = summary;
            return this;
        }

        public SimplifiedLogBuilder WithAddedProperties(Dictionary<string, object?> properties)
        {
            EnsureChangesExists();
            _logEntry.Changes!.Added = properties;
            return this;
        }

        public SimplifiedLogBuilder WithModifiedProperties(Dictionary<string, PropertyChange> properties)
        {
            EnsureChangesExists();
            _logEntry.Changes!.Modified = properties;
            return this;
        }

        public SimplifiedLogBuilder WithDeletedProperties(Dictionary<string, object?> properties)
        {
            EnsureChangesExists();
            _logEntry.Changes!.Deleted = properties;
            return this;
        }

        public SimplifiedLogBuilder WithRelatedEntity(string entityType, string entityId, string relationType, string? description = null)
        {
            _logEntry.RelatedEntities ??= new List<RelatedEntityInfo>();
            _logEntry.RelatedEntities.Add(new RelatedEntityInfo
            {
                EntityType = entityType,
                EntityId = entityId,
                RelationType = relationType,
                Description = description
            });
            return this;
        }

        public SimplifiedLogBuilder WithMetadata(string key, string value)
        {
            _logEntry.Metadata ??= new Dictionary<string, string>();
            _logEntry.Metadata[key] = value;
            return this;
        }

        public SimplifiedLogEntry Build()
        {
            // 自動生成摘要（如果未提供）
            if (string.IsNullOrEmpty(_logEntry.Summary))
            {
                _logEntry.Summary = GenerateAutoSummary();
            }

            return _logEntry;
        }

        private void EnsureChangesExists()
        {
            _logEntry.Changes ??= new SimplifiedChangeData();
        }

        private string GenerateAutoSummary()
        {
            var operation = _logEntry.Operation.ToLowerInvariant();
            var entityType = _logEntry.EntityType;
            var changeCount = _logEntry.Changes?.ChangeCount ?? 0;

            return operation switch
            {
                "create" => $"建立新的 {entityType}",
                "update" => $"更新 {entityType}，變更 {changeCount} 個屬性",
                "delete" => $"刪除 {entityType}",
                _ => $"{operation} 操作於 {entityType}"
            };
        }
    }

    /// <summary>
    /// 簡化日誌轉換器
    /// 將複雜的實體變更轉換為簡化的日誌結構
    /// </summary>
    public static class SimplifiedLogConverter
    {
        /// <summary>
        /// 從 EntityLoggingDTO 轉換為簡化日誌
        /// </summary>
        /// <param name="entityDto">實體日誌 DTO</param>
        /// <param name="transactionId">交易ID</param>
        /// <param name="source">來源</param>
        /// <returns>簡化日誌項目</returns>
        public static SimplifiedLogEntry FromEntityLoggingDTO(EntityLoggingDTO entityDto, string transactionId, string source)
        {
            var builder = new SimplifiedLogBuilder()
                .WithTransactionId(transactionId)
                .WithSource(source)
                .WithEntity(entityDto.EntityType, entityDto.EntityId)
                .WithUser(entityDto.UserId);

            // 根據實體狀態設定操作類型和變更資料
            switch (entityDto.EntityState.ToUpperInvariant())
            {
                case "ADDED":
                    builder.WithOperation("CREATE");
                    if (entityDto.Properties?.Any() == true)
                    {
                        builder.WithAddedProperties(entityDto.Properties);
                    }
                    break;

                case "MODIFIED":
                    builder.WithOperation("UPDATE");
                    if (entityDto.ChangedProperties?.Any() == true && entityDto.Properties != null && entityDto.OriginalProperties != null)
                    {
                        var modifiedProperties = new Dictionary<string, PropertyChange>();
                        foreach (var propName in entityDto.ChangedProperties)
                        {
                            var currentValue = entityDto.Properties.GetValueOrDefault(propName);
                            var originalValue = entityDto.OriginalProperties.GetValueOrDefault(propName);

                            modifiedProperties[propName] = new PropertyChange
                            {
                                From = originalValue,
                                To = currentValue,
                                ChangeType = "Modified",
                                IsSignificant = IsSignificantChange(originalValue, currentValue)
                            };
                        }
                        builder.WithModifiedProperties(modifiedProperties);
                    }
                    break;

                case "DELETED":
                    builder.WithOperation("DELETE");
                    if (entityDto.Properties?.Any() == true)
                    {
                        builder.WithDeletedProperties(entityDto.Properties);
                    }
                    break;
            }

            // 添加相關實體資訊
            if (entityDto.ParentEntity != null)
            {
                builder.WithRelatedEntity(
                    entityDto.ParentEntity.EntityType,
                    entityDto.ParentEntity.EntityId,
                    "Parent",
                    "父實體"
                );
            }

            // 添加實體路徑作為元數據
            if (!string.IsNullOrEmpty(entityDto.EntityPath))
            {
                builder.WithMetadata("EntityPath", entityDto.EntityPath);
            }

            return builder.Build();
        }

        /// <summary>
        /// 批量轉換實體變更日誌
        /// </summary>
        /// <param name="changeLog">實體變更日誌</param>
        /// <returns>簡化日誌項目清單</returns>
        public static List<SimplifiedLogEntry> FromEntityChangeLog(EntityChangeLogDTO changeLog)
        {
            var result = new List<SimplifiedLogEntry>();

            foreach (var entity in changeLog.ChangedEntities)
            {
                try
                {
                    var simplifiedLog = FromEntityLoggingDTO(entity, changeLog.TransactionId, changeLog.Source);
                    result.Add(simplifiedLog);
                }
                catch (Exception ex)
                {
                    // 如果轉換失敗，建立錯誤日誌項目
                    var errorLog = new SimplifiedLogBuilder()
                        .WithTransactionId(changeLog.TransactionId)
                        .WithSource(changeLog.Source)
                        .WithEntity(entity.EntityType, entity.EntityId)
                        .WithOperation("ERROR")
                        .WithSummary($"日誌轉換失敗: {ex.Message}")
                        .WithMetadata("Error", ex.Message)
                        .WithMetadata("ErrorType", ex.GetType().Name)
                        .Build();

                    result.Add(errorLog);
                }
            }

            return result;
        }

        /// <summary>
        /// 判斷是否為重要變更
        /// </summary>
        /// <param name="originalValue">原始值</param>
        /// <param name="currentValue">當前值</param>
        /// <returns>是否為重要變更</returns>
        private static bool IsSignificantChange(object? originalValue, object? currentValue)
        {
            // 簡單的重要性判斷邏輯
            if (originalValue == null && currentValue == null) return false;
            if (originalValue == null || currentValue == null) return true;

            // 對於字串，檢查是否只是空白字符的變更
            if (originalValue is string origStr && currentValue is string currStr)
            {
                return !string.Equals(origStr?.Trim(), currStr?.Trim(), StringComparison.OrdinalIgnoreCase);
            }

            return !originalValue.Equals(currentValue);
        }
    }
}
