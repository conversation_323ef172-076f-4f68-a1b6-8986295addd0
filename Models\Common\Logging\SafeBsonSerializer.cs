using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using FAST_ERP_Backend.Models.Common.Logging;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 安全 BSON 序列化器
    /// 提供多層次的序列化策略，確保日誌記錄不會因序列化失敗而中斷
    /// </summary>
    public static class SafeBsonSerializer
    {
        #region 靜態建構子和初始化

        /// <summary> 是否已初始化 </summary>
        private static bool _isInitialized = false;

        /// <summary> 初始化鎖 </summary>
        private static readonly object _initLock = new object();

        /// <summary>
        /// 初始化 BSON 序列化設定
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            lock (_initLock)
            {
                if (_isInitialized) return;

                try
                {
                    // 註冊序列化約定
                    RegisterSerializationConventions();

                    // 註冊自定義序列化器
                    RegisterCustomSerializers();

                    _isInitialized = true;
                }
                catch (Exception ex)
                {
                    // 初始化失敗不應該影響應用程式啟動
                    Console.WriteLine($"[SafeBsonSerializer] 初始化失敗: {ex.Message}");
                }
            }
        }

        /// <summary> 註冊序列化約定 </summary>
        private static void RegisterSerializationConventions()
        {
            var conventionPack = new ConventionPack
            {
                new CamelCaseElementNameConvention(),
                new IgnoreExtraElementsConvention(true),
                new IgnoreIfNullConvention(true),
                new EnumRepresentationConvention(BsonType.String)
            };

            ConventionRegistry.Register("FastERPLoggingConventions", conventionPack, t => true);
        }

        /// <summary> 註冊自定義序列化器 </summary>
        private static void RegisterCustomSerializers()
        {
            try
            {
                // Guid 序列化為字串
                BsonSerializer.RegisterSerializer(typeof(Guid), new MongoDB.Bson.Serialization.Serializers.GuidSerializer(BsonType.String));
                BsonSerializer.RegisterSerializer(typeof(Guid?), new MongoDB.Bson.Serialization.Serializers.NullableSerializer<Guid>(new MongoDB.Bson.Serialization.Serializers.GuidSerializer(BsonType.String)));
            }
            catch (BsonSerializationException)
            {
                // 序列化器已經註冊，忽略錯誤
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[SafeBsonSerializer] 註冊自定義序列化器失敗: {ex.Message}");
            }
        }

        #endregion

        #region 公開序列化方法

        /// <summary>
        /// 安全序列化物件為字典
        /// 使用新的通用序列化策略，完全避免循環引用問題
        /// </summary>
        /// <param name="obj">要序列化的物件</param>
        /// <returns>序列化結果</returns>
        public static SerializationResult SafeSerialize(object? obj)
        {
            if (obj == null)
            {
                return SerializationResult.Success(new Dictionary<string, object?>(), "Null", 0);
            }

            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 策略 1: 使用循環引用防護器進行安全提取
                var guardResult = TryCircularReferenceGuardSerialization(obj);
                if (guardResult.IsSuccess)
                {
                    stopwatch.Stop();
                    guardResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                    return guardResult;
                }

                // 策略 2: 特殊處理 EntityChangeLogDTO
                if (obj is EntityChangeLogDTO changeLogDto)
                {
                    var changeLogResult = SerializeChangeLog(changeLogDto);
                    stopwatch.Stop();
                    changeLogResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                    return changeLogResult;
                }

                // 策略 3: 檢查是否為 ERPDbContext 的變更日誌結構
                if (IsChangeLogStructure(obj))
                {
                    var changeLogResult = TrySerializeChangeLogStructure(obj);
                    if (changeLogResult.IsSuccess)
                    {
                        stopwatch.Stop();
                        changeLogResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                        return changeLogResult;
                    }
                }

                // 策略 4: 使用 LoggingDataExtractor (向後相容)
                var extractorResult = TryExtractorSerialization(obj);
                if (extractorResult.IsSuccess)
                {
                    stopwatch.Stop();
                    extractorResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                    return extractorResult;
                }

                // 策略 5: 簡化的 JSON 序列化 (備用方案)
                var jsonResult = TrySimplifiedJsonSerialization(obj);
                if (jsonResult.IsSuccess)
                {
                    stopwatch.Stop();
                    jsonResult.ElapsedMilliseconds = stopwatch.ElapsedMilliseconds;
                    return jsonResult;
                }

                // 策略 6: 基本資訊序列化 (最後手段)
                stopwatch.Stop();
                return CreateBasicInfo(obj, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return SerializationResult.Failure(
                    $"所有序列化策略均失敗: {ex.Message}",
                    "AllStrategiesFailed",
                    stopwatch.ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// 序列化實體變更日誌 - 簡化版本
        /// </summary>
        /// <param name="changeLog">變更日誌</param>
        /// <returns>序列化結果</returns>
        public static SerializationResult SerializeChangeLog(EntityChangeLogDTO changeLog)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 直接建立簡化的結構，避免複雜的嵌套
                var result = new Dictionary<string, object?>
                {
                    ["transactionId"] = changeLog.TransactionId,
                    ["source"] = changeLog.Source,
                    ["changeTime"] = changeLog.ChangeTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    ["totalChanges"] = changeLog.TotalChanges,
                    ["entities"] = SerializeEntityListSimplified(changeLog.ChangedEntities)
                };

                stopwatch.Stop();
                return SerializationResult.Success(result, "SimplifiedChangeLog", stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return SerializationResult.Failure($"變更日誌序列化失敗: {ex.Message}", "SimplifiedChangeLog", stopwatch.ElapsedMilliseconds);
            }
        }

        #endregion

        #region 私有序列化策略

        /// <summary> 使用循環引用防護器進行安全序列化 </summary>
        private static SerializationResult TryCircularReferenceGuardSerialization(object obj)
        {
            try
            {
                var guard = new CircularReferenceGuard(maxDepth: 3, maxProperties: 50);
                var result = guard.SafeExtractProperties(obj, obj.GetType().Name);

                if (result.Count == 0)
                {
                    return SerializationResult.Failure("循環引用防護器未能提取任何屬性", "CircularReferenceGuard", 0);
                }

                // 添加序列化元數據
                result["_serializationInfo"] = new Dictionary<string, object?>
                {
                    ["method"] = "CircularReferenceGuard",
                    ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                    ["objectType"] = obj.GetType().Name,
                    ["propertyCount"] = result.Count - 1 // 減去元數據本身
                };

                return SerializationResult.Success(result, "CircularReferenceGuard", 0);
            }
            catch (Exception ex)
            {
                return SerializationResult.Failure($"循環引用防護器序列化失敗: {ex.Message}", "CircularReferenceGuard", 0);
            }
        }

        /// <summary> 嘗試簡化的 JSON 序列化 </summary>
        private static SerializationResult TrySimplifiedJsonSerialization(object obj)
        {
            try
            {
                // 使用 System.Text.Json 進行序列化，然後反序列化為字典
                var options = new JsonSerializerOptions
                {
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
                    ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles, // 忽略循環引用
                    MaxDepth = 10 // 限制序列化深度
                };

                var jsonString = JsonSerializer.Serialize(obj, options);

                // 檢查 JSON 字符串長度，避免過大的序列化結果
                if (jsonString.Length > 100000) // 100KB 限制
                {
                    return SerializationResult.Failure("JSON 序列化結果過大，超過 100KB 限制", "SimplifiedJSON", 0);
                }

                var jsonElement = JsonSerializer.Deserialize<JsonElement>(jsonString);
                var dict = JsonElementToDictionary(jsonElement);

                return SerializationResult.Success(dict, "SimplifiedJSON", 0);
            }
            catch (JsonException jsonEx)
            {
                return SerializationResult.Failure($"JSON 序列化失敗: {jsonEx.Message}", "SimplifiedJSON", 0);
            }
            catch (NotSupportedException notSupportedEx)
            {
                return SerializationResult.Failure($"不支援的類型序列化: {notSupportedEx.Message}", "SimplifiedJSON", 0);
            }
            catch (Exception ex)
            {
                return SerializationResult.Failure($"簡化 JSON 序列化失敗: {ex.Message}", "SimplifiedJSON", 0);
            }
        }

        /// <summary> 嘗試使用資料提取器序列化 </summary>
        private static SerializationResult TryExtractorSerialization(object obj)
        {
            try
            {
                Dictionary<string, object?> result;

                if (obj is EntityLoggingDTO entityDto)
                {
                    result = new Dictionary<string, object?>
                    {
                        ["EntityType"] = entityDto.EntityType,
                        ["EntityId"] = entityDto.EntityId,
                        ["EntityState"] = entityDto.EntityState,
                        ["Timestamp"] = entityDto.Timestamp,
                        ["UserId"] = entityDto.UserId,
                        ["Properties"] = entityDto.Properties,
                        ["OriginalProperties"] = entityDto.OriginalProperties,
                        ["ChangedProperties"] = entityDto.ChangedProperties,
                        ["Metadata"] = entityDto.Metadata
                    };
                }
                else
                {
                    result = LoggingDataExtractor.ExtractSafeProperties(obj);

                    // 檢查提取結果是否有效
                    if (result == null || result.Count == 0)
                    {
                        return SerializationResult.Failure("資料提取器未能提取任何屬性", "Extractor", 0);
                    }
                }

                // 驗證結果的完整性
                if (result.Count > 1000) // 限制屬性數量
                {
                    return SerializationResult.Failure("提取的屬性數量過多，超過 1000 個限制", "Extractor", 0);
                }

                return SerializationResult.Success(result, "Extractor", 0);
            }
            catch (ArgumentException argEx)
            {
                return SerializationResult.Failure($"參數錯誤: {argEx.Message}", "Extractor", 0);
            }
            catch (InvalidOperationException invalidOpEx)
            {
                return SerializationResult.Failure($"操作無效: {invalidOpEx.Message}", "Extractor", 0);
            }
            catch (Exception ex)
            {
                return SerializationResult.Failure($"資料提取器序列化失敗: {ex.Message}", "Extractor", 0);
            }
        }

        /// <summary> 建立基本資訊 (最後手段) </summary>
        private static SerializationResult CreateBasicInfo(object obj, long elapsed)
        {
            var result = new Dictionary<string, object?>
            {
                ["objectType"] = obj.GetType().Name,
                ["fullTypeName"] = obj.GetType().FullName,
                ["toString"] = SafeToString(obj),
                ["serializationType"] = "BasicInfo",
                ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"),
                ["error"] = "所有序列化策略均失敗，僅保留基本資訊"
            };

            // 嘗試提取一些基本屬性資訊
            try
            {
                var type = obj.GetType();
                var properties = type.GetProperties().Take(5); // 只取前 5 個屬性
                var basicProperties = new Dictionary<string, object?>();

                foreach (var prop in properties)
                {
                    try
                    {
                        if (prop.CanRead && prop.GetIndexParameters().Length == 0)
                        {
                            var value = prop.GetValue(obj);
                            basicProperties[prop.Name] = FormatValue(value);
                        }
                    }
                    catch
                    {
                        basicProperties[prop.Name] = "[讀取失敗]";
                    }
                }

                if (basicProperties.Count > 0)
                {
                    result["basicProperties"] = basicProperties;
                }
            }
            catch
            {
                // 忽略基本屬性提取失敗
            }

            return SerializationResult.Success(result, "BasicInfo", elapsed);
        }

        /// <summary> 安全的 ToString 方法 </summary>
        private static string SafeToString(object obj)
        {
            try
            {
                var toString = obj.ToString();
                // 限制字符串長度
                return toString?.Length > 500 ? toString.Substring(0, 500) + "..." : toString ?? "[null]";
            }
            catch
            {
                return "[ToString 失敗]";
            }
        }

        /// <summary> 檢查是否為變更日誌結構 </summary>
        private static bool IsChangeLogStructure(object obj)
        {
            if (obj == null) return false;

            // 檢查是否為匿名類型且包含變更日誌的關鍵屬性
            var type = obj.GetType();
            if (!type.IsAnonymousType()) return false;

            var properties = type.GetProperties();
            var propertyNames = properties.Select(p => p.Name).ToHashSet();

            // 檢查是否包含 ERPDbContext 變更日誌的關鍵屬性
            return propertyNames.Contains("transactionId") &&
                   propertyNames.Contains("entities") &&
                   propertyNames.Contains("totalChanges");
        }

        /// <summary> 嘗試序列化變更日誌結構 </summary>
        private static SerializationResult TrySerializeChangeLogStructure(object obj)
        {
            try
            {
                var type = obj.GetType();
                var properties = type.GetProperties();
                var result = new Dictionary<string, object?>();

                foreach (var prop in properties)
                {
                    var value = prop.GetValue(obj);

                    if (prop.Name == "entities" && value != null)
                    {
                        // 特殊處理實體列表
                        result[prop.Name] = SerializeEntitiesArray(value);
                    }
                    else
                    {
                        result[prop.Name] = FormatValue(value);
                    }
                }

                return SerializationResult.Success(result, "ChangeLogStructure", 0);
            }
            catch (Exception ex)
            {
                return SerializationResult.Failure($"變更日誌結構序列化失敗: {ex.Message}", "ChangeLogStructure", 0);
            }
        }

        /// <summary> 序列化實體陣列 </summary>
        private static object SerializeEntitiesArray(object entitiesValue)
        {
            try
            {
                if (entitiesValue is IEnumerable enumerable && !(entitiesValue is string))
                {
                    var entityList = new List<Dictionary<string, object?>>();

                    foreach (var entity in enumerable)
                    {
                        if (entity != null)
                        {
                            var entityDict = SerializeEntityObject(entity);
                            entityList.Add(entityDict);
                        }
                    }

                    return entityList;
                }

                return FormatValue(entitiesValue);
            }
            catch (Exception ex)
            {
                return new { error = $"實體陣列序列化失敗: {ex.Message}" };
            }
        }

        /// <summary> 序列化單個實體物件 </summary>
        private static Dictionary<string, object?> SerializeEntityObject(object entity)
        {
            var result = new Dictionary<string, object?>();
            var type = entity.GetType();
            var properties = type.GetProperties();

            foreach (var prop in properties.Take(20)) // 限制屬性數量
            {
                try
                {
                    var value = prop.GetValue(entity);

                    if (prop.Name == "data" && value != null)
                    {
                        // 特殊處理 data 屬性，確保詳細資料被保留
                        result[prop.Name] = SerializeDataObject(value);
                    }
                    else
                    {
                        result[prop.Name] = FormatValue(value);
                    }
                }
                catch (Exception ex)
                {
                    result[prop.Name] = $"[屬性讀取錯誤: {ex.Message}]";
                }
            }

            return result;
        }

        /// <summary> 序列化資料物件 </summary>
        private static object SerializeDataObject(object dataValue)
        {
            try
            {
                var type = dataValue.GetType();

                if (type.IsAnonymousType())
                {
                    var result = new Dictionary<string, object?>();
                    var properties = type.GetProperties();

                    foreach (var prop in properties)
                    {
                        var value = prop.GetValue(dataValue);
                        result[prop.Name] = FormatValue(value);
                    }

                    return result;
                }

                return FormatValue(dataValue);
            }
            catch (Exception ex)
            {
                return new { error = $"資料物件序列化失敗: {ex.Message}" };
            }
        }



        #endregion

        #region 輔助方法

        /// <summary> 將 JsonElement 轉換為字典 </summary>
        private static Dictionary<string, object?> JsonElementToDictionary(JsonElement element)
        {
            var result = new Dictionary<string, object?>();

            if (element.ValueKind == JsonValueKind.Object)
            {
                foreach (var property in element.EnumerateObject())
                {
                    result[property.Name] = JsonElementToObject(property.Value);
                }
            }

            return result;
        }

        /// <summary> 將 JsonElement 轉換為 .NET 物件 </summary>
        private static object? JsonElementToObject(JsonElement element)
        {
            return element.ValueKind switch
            {
                JsonValueKind.Null => null,
                JsonValueKind.String => element.GetString(),
                JsonValueKind.Number => element.TryGetInt32(out var intValue) ? intValue : element.GetDecimal(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Array => element.EnumerateArray().Select(JsonElementToObject).ToList(),
                JsonValueKind.Object => JsonElementToDictionary(element),
                _ => element.ToString()
            };
        }

        /// <summary> 序列化實體清單 - 簡化版本 </summary>
        private static List<Dictionary<string, object?>> SerializeEntityListSimplified(List<EntityLoggingDTO> entities)
        {
            var result = new List<Dictionary<string, object?>>();

            foreach (var entity in entities)
            {
                try
                {
                    // 直接建立簡化的實體記錄
                    var entityRecord = new Dictionary<string, object?>
                    {
                        ["entityType"] = entity.EntityType,
                        ["entityId"] = entity.EntityId,
                        ["entityState"] = entity.EntityState,
                        ["timestamp"] = entity.Timestamp.ToString("yyyy-MM-dd HH:mm:ss"),
                        ["userId"] = entity.UserId
                    };

                    // 只記錄變更的屬性，避免冗餘資料
                    if (entity.EntityState == "Modified" && entity.ChangedProperties?.Any() == true)
                    {
                        var changes = new Dictionary<string, object?>();
                        foreach (var propName in entity.ChangedProperties)
                        {
                            if (entity.Properties?.ContainsKey(propName) == true)
                            {
                                var currentValue = entity.Properties[propName];
                                var originalValue = entity.OriginalProperties?.GetValueOrDefault(propName);

                                changes[propName] = new
                                {
                                    from = FormatValue(originalValue),
                                    to = FormatValue(currentValue)
                                };
                            }
                        }
                        entityRecord["changes"] = changes;
                    }
                    else if (entity.EntityState == "Added")
                    {
                        // 新增記錄只記錄關鍵屬性
                        var keyProperties = new Dictionary<string, object?>();
                        if (entity.Properties != null)
                        {
                            foreach (var prop in entity.Properties.Take(10)) // 限制最多 10 個屬性
                            {
                                keyProperties[prop.Key] = FormatValue(prop.Value);
                            }
                        }
                        entityRecord["properties"] = keyProperties;
                    }

                    result.Add(entityRecord);
                }
                catch (Exception ex)
                {
                    // 如果處理失敗，至少保留基本資訊
                    result.Add(new Dictionary<string, object?>
                    {
                        ["entityType"] = entity.EntityType,
                        ["entityId"] = entity.EntityId,
                        ["entityState"] = entity.EntityState,
                        ["error"] = ex.Message
                    });
                }
            }

            return result;
        }

        /// <summary> 格式化值，確保序列化友好 </summary>
        private static object? FormatValue(object? value)
        {
            return value switch
            {
                null => null,
                Guid guid => guid.ToString(),
                DateTime dateTime => dateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("yyyy-MM-dd HH:mm:ss"),
                decimal dec => dec.ToString("F2"),
                _ => value
            };
        }

        #endregion
    }

    /// <summary> 類型擴展方法 </summary>
    internal static class TypeExtensions
    {
        /// <summary> 檢查是否為匿名類型 </summary>
        public static bool IsAnonymousType(this Type type)
        {
            return type.Name.Contains("AnonymousType") &&
                   type.IsGenericType &&
                   type.Attributes.HasFlag(TypeAttributes.NotPublic);
        }
    }
}
