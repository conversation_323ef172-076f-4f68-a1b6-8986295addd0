using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using FAST_ERP_Backend.Models.Common.Logging;
using FAST_ERP_Backend.Models.Ims;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using Xunit.Abstractions;

namespace FAST_ERP_Backend.Tests.Common.Logging
{
    /// <summary>
    /// 通用日誌記錄系統測試
    /// 測試新的通用日誌記錄系統的各種功能和效能
    /// </summary>
    public class GenericLoggingSystemTests : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly TestDbContext _context;

        public GenericLoggingSystemTests(ITestOutputHelper output)
        {
            _output = output;
            _context = CreateTestDbContext();
        }

        #region 基本功能測試

        [Fact]
        public void CircularReferenceGuard_ShouldPreventCircularReferences()
        {
            // Arrange
            var guard = new CircularReferenceGuard(maxDepth: 3, maxProperties: 20);
            var testEntity = CreateTestEntityWithCircularReference();

            // Act
            var result = guard.SafeExtractProperties(testEntity, "testEntity");

            // Assert
            Assert.NotNull(result);
            Assert.True(result.ContainsKey("_circularReference") || !result.Any(kvp => kvp.Value?.ToString()?.Contains("循環引用") == true));
            
            _output.WriteLine($"提取的屬性數量: {result.Count}");
            _output.WriteLine($"結果: {string.Join(", ", result.Keys)}");
        }

        [Fact]
        public void GenericEntityChangeTracker_ShouldExtractEntityChanges()
        {
            // Arrange
            var partner = new Partner
            {
                PartnerID = Guid.NewGuid(),
                IsStop = false
            };

            _context.Ims_Partner.Add(partner);
            var entry = _context.Entry(partner);

            // Act
            var result = GenericEntityChangeTracker.ExtractEntityChange(entry, _context);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Partner", result.EntityType);
            Assert.Equal("Added", result.EntityState);
            Assert.NotNull(result.Properties);
            Assert.True(result.Properties.Count > 0);

            _output.WriteLine($"實體類型: {result.EntityType}");
            _output.WriteLine($"實體狀態: {result.EntityState}");
            _output.WriteLine($"屬性數量: {result.Properties.Count}");
        }

        [Fact]
        public void SimplifiedLogConverter_ShouldConvertEntityLoggingDTO()
        {
            // Arrange
            var entityDto = new EntityLoggingDTO
            {
                EntityType = "Partner",
                EntityId = Guid.NewGuid().ToString(),
                EntityState = "Added",
                Properties = new Dictionary<string, object?>
                {
                    ["PartnerID"] = Guid.NewGuid(),
                    ["IsStop"] = false
                }
            };

            // Act
            var result = SimplifiedLogConverter.FromEntityLoggingDTO(entityDto, "test-transaction", "TestSource");

            // Assert
            Assert.NotNull(result);
            Assert.Equal("CREATE", result.Operation);
            Assert.Equal("Partner", result.EntityType);
            Assert.NotNull(result.Changes);
            Assert.NotNull(result.Changes.Added);
            Assert.True(result.Changes.Added.Count > 0);

            _output.WriteLine($"操作: {result.Operation}");
            _output.WriteLine($"摘要: {result.Summary}");
            _output.WriteLine($"變更數量: {result.Changes.ChangeCount}");
        }

        #endregion

        #region 效能測試

        [Fact]
        public void PerformanceOptimizer_ShouldCacheTypeInfo()
        {
            // Arrange
            var type = typeof(Partner);
            var stopwatch = Stopwatch.StartNew();

            // Act - 第一次呼叫（應該建立快取）
            var firstCall = LoggingPerformanceOptimizer.GetCachedTypeInfo(type);
            var firstCallTime = stopwatch.ElapsedMilliseconds;

            stopwatch.Restart();

            // Act - 第二次呼叫（應該使用快取）
            var secondCall = LoggingPerformanceOptimizer.GetCachedTypeInfo(type);
            var secondCallTime = stopwatch.ElapsedMilliseconds;

            // Assert
            Assert.NotNull(firstCall);
            Assert.NotNull(secondCall);
            Assert.Same(firstCall, secondCall); // 應該是同一個實例
            Assert.True(secondCallTime < firstCallTime, "第二次呼叫應該更快（使用快取）");

            _output.WriteLine($"第一次呼叫時間: {firstCallTime}ms");
            _output.WriteLine($"第二次呼叫時間: {secondCallTime}ms");
            _output.WriteLine($"快取效果: {((double)(firstCallTime - secondCallTime) / firstCallTime * 100):F1}% 提升");
        }

        [Fact]
        public void PerformanceOptimizer_ShouldLimitProperties()
        {
            // Arrange
            var largePropertyDict = new Dictionary<string, object?>();
            for (int i = 0; i < 100; i++)
            {
                largePropertyDict[$"Property{i}"] = $"Value{i}";
            }

            // Act
            var result = LoggingPerformanceOptimizer.LimitProperties(largePropertyDict);

            // Assert
            Assert.True(result.Count <= 51); // 50 + 1 for truncation message
            Assert.True(result.ContainsKey("_truncated"));

            _output.WriteLine($"原始屬性數量: {largePropertyDict.Count}");
            _output.WriteLine($"限制後屬性數量: {result.Count}");
        }

        [Fact]
        public void PerformanceOptimizer_ShouldLimitStringLength()
        {
            // Arrange
            var longString = new string('A', 2000);

            // Act
            var result = LoggingPerformanceOptimizer.LimitStringLength(longString);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.ToString()!.Length < longString.Length);
            Assert.True(result.ToString()!.Contains("截斷"));

            _output.WriteLine($"原始字串長度: {longString.Length}");
            _output.WriteLine($"限制後字串長度: {result.ToString()!.Length}");
        }

        #endregion

        #region 複雜場景測試

        [Fact]
        public async Task ComplexEntityHierarchy_ShouldLogCorrectly()
        {
            // Arrange
            var partner = new Partner
            {
                PartnerID = Guid.NewGuid(),
                IsStop = false,
                IndividualDetail = new IndividualDetail
                {
                    PartnerID = Guid.NewGuid(),
                    FirstName = "測試",
                    LastName = "用戶"
                }
            };

            // Act
            _context.Ims_Partner.Add(partner);
            var entries = _context.ChangeTracker.Entries().Where(e => e.State != EntityState.Unchanged);
            var changeLog = GenericEntityChangeTracker.ProcessEntityChanges(entries, _context);

            // Assert
            Assert.NotNull(changeLog);
            Assert.True(changeLog.ChangedEntities.Count > 0);
            
            var partnerLog = changeLog.ChangedEntities.FirstOrDefault(e => e.EntityType == "Partner");
            Assert.NotNull(partnerLog);

            _output.WriteLine($"變更實體數量: {changeLog.ChangedEntities.Count}");
            foreach (var entity in changeLog.ChangedEntities)
            {
                _output.WriteLine($"- {entity.EntityType}: {entity.EntityState}");
            }
        }

        [Fact]
        public void SafeBsonSerializer_ShouldHandleComplexObjects()
        {
            // Arrange
            var complexObject = new
            {
                SimpleProperty = "測試值",
                NumberProperty = 123,
                DateProperty = DateTime.Now,
                NestedObject = new
                {
                    NestedProperty = "嵌套值",
                    DeepNested = new
                    {
                        DeepProperty = "深層值"
                    }
                },
                ListProperty = new List<string> { "項目1", "項目2", "項目3" }
            };

            // Act
            var result = SafeBsonSerializer.SafeSerialize(complexObject);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Data);
            Assert.True(result.Data.Count > 0);

            _output.WriteLine($"序列化方法: {result.SerializationMethod}");
            _output.WriteLine($"序列化時間: {result.ElapsedMilliseconds}ms");
            _output.WriteLine($"資料項目數: {result.Data.Count}");
        }

        #endregion

        #region 壓力測試

        [Fact]
        public async Task StressTest_ShouldHandleManyEntities()
        {
            // Arrange
            const int entityCount = 100;
            var stopwatch = Stopwatch.StartNew();

            // Act
            for (int i = 0; i < entityCount; i++)
            {
                var partner = new Partner
                {
                    PartnerID = Guid.NewGuid(),
                    IsStop = i % 2 == 0
                };
                _context.Ims_Partner.Add(partner);
            }

            var entries = _context.ChangeTracker.Entries().Where(e => e.State != EntityState.Unchanged);
            var changeLog = GenericEntityChangeTracker.ProcessEntityChanges(entries, _context);

            stopwatch.Stop();

            // Assert
            Assert.Equal(entityCount, changeLog.ChangedEntities.Count);
            Assert.True(stopwatch.ElapsedMilliseconds < 5000, "處理100個實體應該在5秒內完成");

            _output.WriteLine($"處理 {entityCount} 個實體耗時: {stopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"平均每個實體: {(double)stopwatch.ElapsedMilliseconds / entityCount:F2}ms");
        }

        #endregion

        #region 輔助方法

        private TestDbContext CreateTestDbContext()
        {
            var options = new DbContextOptionsBuilder<TestDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            return new TestDbContext(options);
        }

        private object CreateTestEntityWithCircularReference()
        {
            var entity1 = new TestEntity { Id = 1, Name = "Entity1" };
            var entity2 = new TestEntity { Id = 2, Name = "Entity2" };
            
            entity1.RelatedEntity = entity2;
            entity2.RelatedEntity = entity1; // 循環引用

            return entity1;
        }

        public void Dispose()
        {
            _context?.Dispose();
        }

        #endregion
    }

    #region 測試用類別

    public class TestDbContext : DbContext
    {
        public TestDbContext(DbContextOptions<TestDbContext> options) : base(options) { }

        public DbSet<Partner> Ims_Partner { get; set; } = null!;
        public DbSet<IndividualDetail> Ims_IndividualDetail { get; set; } = null!;
    }

    public class TestEntity
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public TestEntity? RelatedEntity { get; set; }
    }

    #endregion
}
