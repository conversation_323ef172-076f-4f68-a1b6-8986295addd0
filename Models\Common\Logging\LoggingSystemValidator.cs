using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 日誌記錄系統驗證器
    /// 提供簡單的驗證方法來測試新的通用日誌記錄系統
    /// </summary>
    public static class LoggingSystemValidator
    {
        /// <summary>
        /// 驗證循環引用防護功能
        /// </summary>
        /// <returns>驗證結果</returns>
        public static ValidationResult ValidateCircularReferenceGuard()
        {
            try
            {
                var guard = new CircularReferenceGuard(maxDepth: 3, maxProperties: 20);
                var testEntity = CreateTestEntityWithCircularReference();

                var stopwatch = Stopwatch.StartNew();
                var result = guard.SafeExtractProperties(testEntity, "testEntity");
                stopwatch.Stop();

                var hasCircularReferenceProtection = result.ContainsKey("_circularReference") || 
                    result.Values.Any(v => v?.ToString()?.Contains("循環引用") == true);

                return new ValidationResult
                {
                    IsSuccess = result.Count > 0,
                    Message = hasCircularReferenceProtection 
                        ? "循環引用防護正常工作" 
                        : $"成功提取 {result.Count} 個屬性",
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                    Details = new Dictionary<string, object>
                    {
                        ["propertyCount"] = result.Count,
                        ["hasCircularProtection"] = hasCircularReferenceProtection,
                        ["extractedProperties"] = result.Keys.Take(5).ToList()
                    }
                };
            }
            catch (Exception ex)
            {
                return ValidationResult.Failure($"循環引用防護驗證失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 驗證通用實體變更追蹤功能
        /// </summary>
        /// <param name="context">資料庫上下文</param>
        /// <returns>驗證結果</returns>
        public static ValidationResult ValidateGenericEntityChangeTracker(DbContext context)
        {
            try
            {
                var partner = new Partner
                {
                    PartnerID = Guid.NewGuid(),
                    IsStop = false
                };

                context.Entry(partner).State = EntityState.Added;
                var entry = context.Entry(partner);

                var stopwatch = Stopwatch.StartNew();
                var result = GenericEntityChangeTracker.ExtractEntityChange(entry, context);
                stopwatch.Stop();

                var isValid = result != null && 
                             result.EntityType == "Partner" && 
                             result.EntityState == "Added" && 
                             result.Properties?.Count > 0;

                return new ValidationResult
                {
                    IsSuccess = isValid,
                    Message = isValid 
                        ? "通用實體變更追蹤正常工作" 
                        : "實體變更追蹤驗證失敗",
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                    Details = new Dictionary<string, object>
                    {
                        ["entityType"] = result?.EntityType ?? "null",
                        ["entityState"] = result?.EntityState ?? "null",
                        ["propertyCount"] = result?.Properties?.Count ?? 0,
                        ["hasUserId"] = !string.IsNullOrEmpty(result?.UserId)
                    }
                };
            }
            catch (Exception ex)
            {
                return ValidationResult.Failure($"實體變更追蹤驗證失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 驗證簡化日誌轉換功能
        /// </summary>
        /// <returns>驗證結果</returns>
        public static ValidationResult ValidateSimplifiedLogConverter()
        {
            try
            {
                var entityDto = new EntityLoggingDTO
                {
                    EntityType = "Partner",
                    EntityId = Guid.NewGuid().ToString(),
                    EntityState = "Added",
                    Properties = new Dictionary<string, object?>
                    {
                        ["PartnerID"] = Guid.NewGuid(),
                        ["IsStop"] = false
                    }
                };

                var stopwatch = Stopwatch.StartNew();
                var result = SimplifiedLogConverter.FromEntityLoggingDTO(entityDto, "test-transaction", "TestSource");
                stopwatch.Stop();

                var isValid = result != null && 
                             result.Operation == "CREATE" && 
                             result.EntityType == "Partner" && 
                             result.Changes?.Added?.Count > 0;

                return new ValidationResult
                {
                    IsSuccess = isValid,
                    Message = isValid 
                        ? "簡化日誌轉換正常工作" 
                        : "簡化日誌轉換驗證失敗",
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                    Details = new Dictionary<string, object>
                    {
                        ["operation"] = result?.Operation ?? "null",
                        ["entityType"] = result?.EntityType ?? "null",
                        ["changeCount"] = result?.Changes?.ChangeCount ?? 0,
                        ["summary"] = result?.Summary ?? "null"
                    }
                };
            }
            catch (Exception ex)
            {
                return ValidationResult.Failure($"簡化日誌轉換驗證失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 驗證效能優化器功能
        /// </summary>
        /// <returns>驗證結果</returns>
        public static ValidationResult ValidatePerformanceOptimizer()
        {
            try
            {
                var type = typeof(Partner);
                var stopwatch = Stopwatch.StartNew();

                // 第一次呼叫（建立快取）
                var firstCall = LoggingPerformanceOptimizer.GetCachedTypeInfo(type);
                var firstCallTime = stopwatch.ElapsedMilliseconds;

                stopwatch.Restart();

                // 第二次呼叫（使用快取）
                var secondCall = LoggingPerformanceOptimizer.GetCachedTypeInfo(type);
                var secondCallTime = stopwatch.ElapsedMilliseconds;

                var isCacheWorking = ReferenceEquals(firstCall, secondCall) && secondCallTime <= firstCallTime;

                // 測試屬性限制
                var largeDict = new Dictionary<string, object?>();
                for (int i = 0; i < 100; i++)
                {
                    largeDict[$"Property{i}"] = $"Value{i}";
                }
                var limitedDict = LoggingPerformanceOptimizer.LimitProperties(largeDict);

                // 測試字串限制
                var longString = new string('A', 2000);
                var limitedString = LoggingPerformanceOptimizer.LimitStringLength(longString);

                var stats = LoggingPerformanceOptimizer.GetPerformanceStatistics();

                return new ValidationResult
                {
                    IsSuccess = isCacheWorking && limitedDict.Count <= 51 && limitedString.ToString()!.Length < longString.Length,
                    Message = "效能優化器功能驗證完成",
                    ElapsedMilliseconds = firstCallTime + secondCallTime,
                    Details = new Dictionary<string, object>
                    {
                        ["cacheWorking"] = isCacheWorking,
                        ["firstCallTime"] = firstCallTime,
                        ["secondCallTime"] = secondCallTime,
                        ["propertyLimitWorking"] = limitedDict.Count <= 51,
                        ["stringLimitWorking"] = limitedString.ToString()!.Length < longString.Length,
                        ["cacheSize"] = stats["cacheSize"]
                    }
                };
            }
            catch (Exception ex)
            {
                return ValidationResult.Failure($"效能優化器驗證失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 驗證安全序列化器功能
        /// </summary>
        /// <returns>驗證結果</returns>
        public static ValidationResult ValidateSafeBsonSerializer()
        {
            try
            {
                var complexObject = new
                {
                    SimpleProperty = "測試值",
                    NumberProperty = 123,
                    DateProperty = DateTime.Now,
                    NestedObject = new
                    {
                        NestedProperty = "嵌套值"
                    }
                };

                var stopwatch = Stopwatch.StartNew();
                var result = SafeBsonSerializer.SafeSerialize(complexObject);
                stopwatch.Stop();

                return new ValidationResult
                {
                    IsSuccess = result.IsSuccess && result.Data != null && result.Data.Count > 0,
                    Message = result.IsSuccess 
                        ? $"安全序列化器正常工作，使用方法: {result.SerializationMethod}" 
                        : $"序列化失敗: {result.ErrorMessage}",
                    ElapsedMilliseconds = stopwatch.ElapsedMilliseconds,
                    Details = new Dictionary<string, object>
                    {
                        ["serializationMethod"] = result.SerializationMethod,
                        ["isSuccess"] = result.IsSuccess,
                        ["dataCount"] = result.Data?.Count ?? 0,
                        ["serializationTime"] = result.ElapsedMilliseconds,
                        ["errorMessage"] = result.ErrorMessage ?? "無錯誤"
                    }
                };
            }
            catch (Exception ex)
            {
                return ValidationResult.Failure($"安全序列化器驗證失敗: {ex.Message}");
            }
        }

        /// <summary>
        /// 執行完整的系統驗證
        /// </summary>
        /// <param name="context">資料庫上下文（可選）</param>
        /// <returns>完整驗證結果</returns>
        public static async Task<CompleteValidationResult> ValidateCompleteSystem(DbContext? context = null)
        {
            var results = new List<ValidationResult>();

            // 執行各項驗證
            results.Add(ValidateCircularReferenceGuard());
            results.Add(ValidateSimplifiedLogConverter());
            results.Add(ValidatePerformanceOptimizer());
            results.Add(ValidateSafeBsonSerializer());

            if (context != null)
            {
                results.Add(ValidateGenericEntityChangeTracker(context));
            }

            var totalTime = results.Sum(r => r.ElapsedMilliseconds);
            var successCount = results.Count(r => r.IsSuccess);

            return new CompleteValidationResult
            {
                IsSuccess = successCount == results.Count,
                TotalTests = results.Count,
                SuccessfulTests = successCount,
                FailedTests = results.Count - successCount,
                TotalElapsedMilliseconds = totalTime,
                Results = results,
                Summary = $"驗證完成: {successCount}/{results.Count} 項測試通過，總耗時 {totalTime}ms"
            };
        }

        /// <summary>
        /// 建立測試用的循環引用實體
        /// </summary>
        private static object CreateTestEntityWithCircularReference()
        {
            var entity1 = new TestEntity { Id = 1, Name = "Entity1" };
            var entity2 = new TestEntity { Id = 2, Name = "Entity2" };
            
            entity1.RelatedEntity = entity2;
            entity2.RelatedEntity = entity1; // 循環引用

            return entity1;
        }

        /// <summary>
        /// 測試用實體類別
        /// </summary>
        private class TestEntity
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public TestEntity? RelatedEntity { get; set; }
        }
    }

    /// <summary>
    /// 驗證結果
    /// </summary>
    public class ValidationResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public long ElapsedMilliseconds { get; set; }
        public Dictionary<string, object> Details { get; set; } = new();

        public static ValidationResult Failure(string message)
        {
            return new ValidationResult
            {
                IsSuccess = false,
                Message = message,
                ElapsedMilliseconds = 0,
                Details = new Dictionary<string, object>()
            };
        }
    }

    /// <summary>
    /// 完整驗證結果
    /// </summary>
    public class CompleteValidationResult
    {
        public bool IsSuccess { get; set; }
        public int TotalTests { get; set; }
        public int SuccessfulTests { get; set; }
        public int FailedTests { get; set; }
        public long TotalElapsedMilliseconds { get; set; }
        public List<ValidationResult> Results { get; set; } = new();
        public string Summary { get; set; } = string.Empty;
    }
}
