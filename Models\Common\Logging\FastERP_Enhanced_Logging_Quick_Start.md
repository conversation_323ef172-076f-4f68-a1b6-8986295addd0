# FastERP 增強型日誌記錄系統 - 快速使用指南

## 🚀 **立即開始**

### **系統狀態確認**
新的增強型日誌記錄系統已完全整合到 FastERP 中，無需任何配置即可使用！

```bash
# 1. 啟動 FastERP 後端
cd e:\Project\FastERP\fast_erp_backend
dotnet run

# 2. 驗證系統狀態
curl -X GET "http://localhost:5000/api/LoggingTest/system-status"
```

**預期回應：**
```json
{
  "success": true,
  "message": "增強型日誌記錄系統運行正常",
  "features": {
    "enhancedEntityChangeTracker": "已啟用",
    "circularReferenceGuard": "已啟用",
    "simplifiedLogFormat": "已啟用",
    "performanceOptimization": "已啟用",
    "complexEntitySupport": "已啟用"
  }
}
```

---

## 🧪 **快速測試**

### **1. 測試新增實體日誌記錄**
```bash
curl -X POST "http://localhost:5000/api/LoggingTest/test-create"
```

**功能：** 創建一個測試 Partner 實體，驗證新增操作的日誌記錄

### **2. 測試修改實體日誌記錄**
```bash
# 使用步驟1返回的 partnerId
curl -X PUT "http://localhost:5000/api/LoggingTest/test-update/{partnerId}"
```

**功能：** 修改 Partner 實體屬性，驗證修改操作的前後差異記錄

### **3. 測試刪除實體日誌記錄**
```bash
curl -X DELETE "http://localhost:5000/api/LoggingTest/test-delete/{partnerId}"
```

**功能：** 刪除 Partner 實體，驗證刪除操作的日誌記錄

### **4. 測試批量操作日誌記錄**
```bash
curl -X POST "http://localhost:5000/api/LoggingTest/test-batch"
```

**功能：** 批量創建多個 Partner 實體，驗證批次操作的整合日誌記錄

---

## 📋 **日誌格式說明**

### **新增操作日誌**
```json
{
  "operation": "CREATE",
  "entityType": "Partner",
  "entityId": "12345678-1234-1234-1234-123456789012",
  "createdData": {
    "PartnerID": "12345678-1234-1234-1234-123456789012",
    "IsStop": false,
    "CreateTime": 1704067200000,
    "CreateUserId": "TEST_USER"
  },
  "propertyCount": 4
}
```

### **修改操作日誌**
```json
{
  "operation": "UPDATE",
  "entityType": "Partner",
  "entityId": "12345678-1234-1234-1234-123456789012",
  "modifications": {
    "IsStop": {
      "before": false,
      "after": true,
      "changed": true
    },
    "UpdateTime": {
      "before": 1704067200000,
      "after": 1704067260000,
      "changed": true
    }
  },
  "changedProperties": ["IsStop", "UpdateTime"],
  "changeCount": 2
}
```

### **刪除操作日誌**
```json
{
  "operation": "DELETE",
  "entityType": "Partner",
  "entityId": "12345678-1234-1234-1234-123456789012",
  "deletedData": {
    "PartnerID": "12345678-1234-1234-1234-123456789012",
    "IsStop": true,
    "CreateTime": 1704067200000,
    "UpdateTime": 1704067260000
  },
  "propertyCount": 4
}
```

### **批次操作摘要日誌**
```json
{
  "operation": "BATCH",
  "transactionId": "87654321-4321-4321-4321-210987654321",
  "totalChanges": 3,
  "operationCounts": {
    "ADDED": 3
  },
  "entityTypeCounts": {
    "Partner": 3
  },
  "captureTime": "2024-01-01 12:00:00"
}
```

---

## 🔍 **MongoDB 日誌查詢**

### **查詢特定實體的變更記錄**
```javascript
// MongoDB 查詢範例
db.logs.find({
  "Data.entityType": "Partner",
  "Data.entityId": "12345678-1234-1234-1234-123456789012"
}).sort({ "Timestamp": -1 })
```

### **查詢特定時間範圍的變更**
```javascript
db.logs.find({
  "Timestamp": {
    $gte: ISODate("2024-01-01T00:00:00Z"),
    $lte: ISODate("2024-01-02T00:00:00Z")
  }
}).sort({ "Timestamp": -1 })
```

### **查詢特定操作類型**
```javascript
db.logs.find({
  "Data.operation": "UPDATE"
}).sort({ "Timestamp": -1 })
```

---

## ⚡ **自動功能**

### **無需配置的功能**
- ✅ **自動啟用**：系統啟動時自動啟用新的日誌記錄功能
- ✅ **自動追蹤**：所有 Entity Framework 實體變更自動追蹤
- ✅ **自動格式化**：日誌自動格式化為清晰易讀的結構
- ✅ **自動防護**：循環引用自動防護，無需擔心序列化問題

### **智能優化功能**
- ✅ **智能快取**：實體元數據自動快取，提升效能
- ✅ **智能過濾**：只記錄安全類型屬性，避免敏感資料
- ✅ **智能批次**：同一交易的多個變更自動整合
- ✅ **智能錯誤處理**：單一實體錯誤不影響整體處理

---

## 🛠️ **開發者工具**

### **日誌記錄驗證**
```csharp
// 在您的服務中驗證日誌記錄
public async Task<bool> VerifyLoggingAsync()
{
    // 創建測試實體
    var partner = new Partner { /* ... */ };
    _context.Ims_Partner.Add(partner);
    
    // 保存變更（自動觸發日誌記錄）
    await _context.SaveChangesAsync();
    
    // 日誌已自動記錄到 MongoDB
    return true;
}
```

### **自定義日誌查詢**
```csharp
// 查詢特定實體的變更歷史
public async Task<List<LogEntry>> GetEntityHistoryAsync(string entityId)
{
    // 透過 ILoggerService 查詢 MongoDB
    return await _loggerService.GetEntityLogsAsync("Partner", entityId);
}
```

---

## 🎯 **最佳實踐**

### **1. 日誌監控**
- 定期檢查 MongoDB 中的日誌品質
- 監控日誌記錄的效能影響
- 驗證複雜實體關係的記錄完整性

### **2. 效能優化**
- 系統已自動優化，無需額外配置
- 如有特殊需求，可調整 `EnhancedEntityChangeTracker` 的快取策略

### **3. 錯誤處理**
- 日誌記錄錯誤不會影響主要業務流程
- 錯誤會記錄到 Console 和 MongoDB 中
- 可透過測試端點驗證系統健康狀況

---

## 🎉 **完成！**

FastERP 增強型日誌記錄系統現在已完全運行！

**主要優勢：**
- 🚀 **零配置**：立即可用，無需額外設定
- 🔍 **完整追蹤**：所有實體變更完整記錄
- 🛡️ **安全可靠**：循環引用問題完全解決
- ⚡ **高效能**：智能優化，不影響業務流程
- 📊 **易讀格式**：清晰的 JSON 結構，便於分析

開始使用新的日誌記錄系統，享受企業級的資料變更追蹤能力！
