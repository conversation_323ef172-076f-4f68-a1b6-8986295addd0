/**
 * FastERP Partner 模組增強日誌系統測試腳本
 * 測試 Partner 實體的嵌套變更追蹤和完整審計記錄
 */

const https = require('https');

// 配置
const config = {
    baseUrl: 'https://localhost:7137',
    timeout: 30000,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
};

// 忽略自簽名證書錯誤
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

/**
 * 發送 HTTP 請求
 */
function makeRequest(method, path, data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(path, config.baseUrl);
        
        const options = {
            method: method,
            headers: config.headers,
            timeout: config.timeout
        };

        const req = https.request(url, options, (res) => {
            let body = '';
            
            res.on('data', (chunk) => {
                body += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = {
                        status: res.statusCode,
                        headers: res.headers,
                        data: body ? JSON.parse(body) : null
                    };
                    resolve(response);
                } catch (error) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: body,
                        parseError: error.message
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

/**
 * 測試 Partner 實體創建和日誌記錄
 */
async function testPartnerCreation() {
    console.log('\n🔄 測試 Partner 實體創建和日誌記錄...');
    
    const testPartner = {
        name: `測試合作夥伴_${Date.now()}`,
        type: "Both",
        isStop: false,
        individualDetail: {
            firstName: "測試",
            lastName: "用戶",
            birthDate: "1990-01-01",
            gender: "Male",
            settlementDay: 15
        },
        customerDetail: {
            customerCategoryID: null,
            creditLimit: 100000,
            paymentTerms: "Net30"
        },
        supplierDetail: {
            supplierCategoryID: null,
            paymentTerms: "Net15"
        }
    };

    try {
        const response = await makeRequest('POST', '/api/Partner', testPartner);
        
        if (response.status === 200 || response.status === 201) {
            console.log('✅ Partner 創建成功');
            console.log(`   Partner ID: ${response.data?.data?.id || 'N/A'}`);
            return response.data?.data;
        } else {
            console.log(`❌ Partner 創建失敗: ${response.status}`);
            console.log(`   錯誤: ${JSON.stringify(response.data, null, 2)}`);
            return null;
        }
    } catch (error) {
        console.log(`❌ Partner 創建請求失敗: ${error.message}`);
        return null;
    }
}

/**
 * 測試 Partner 實體修改和嵌套日誌記錄
 */
async function testPartnerModification(partnerId) {
    console.log('\n🔄 測試 Partner 實體修改和嵌套日誌記錄...');
    
    if (!partnerId) {
        console.log('❌ 無效的 Partner ID，跳過修改測試');
        return false;
    }

    // 首先獲取現有的 Partner 資料
    try {
        const getResponse = await makeRequest('GET', `/api/Partner/${partnerId}`);
        
        if (getResponse.status !== 200) {
            console.log(`❌ 無法獲取 Partner 資料: ${getResponse.status}`);
            return false;
        }

        const existingPartner = getResponse.data?.data;
        if (!existingPartner) {
            console.log('❌ Partner 資料為空');
            return false;
        }

        // 修改 Partner 資料，包括嵌套實體
        const updatedPartner = {
            ...existingPartner,
            name: `${existingPartner.name}_已修改`,
            individualDetail: {
                ...existingPartner.individualDetail,
                settlementDay: 25, // 這是關鍵測試：修改嵌套實體的屬性
                firstName: "修改後的名字"
            },
            customerDetail: {
                ...existingPartner.customerDetail,
                creditLimit: 150000 // 修改客戶信用額度
            }
        };

        console.log('📝 準備修改的資料:');
        console.log(`   名稱: ${existingPartner.name} → ${updatedPartner.name}`);
        console.log(`   結算日: ${existingPartner.individualDetail?.settlementDay} → ${updatedPartner.individualDetail?.settlementDay}`);
        console.log(`   信用額度: ${existingPartner.customerDetail?.creditLimit} → ${updatedPartner.customerDetail?.creditLimit}`);

        const updateResponse = await makeRequest('PUT', `/api/Partner/${partnerId}`, updatedPartner);
        
        if (updateResponse.status === 200) {
            console.log('✅ Partner 修改成功');
            console.log('   這應該觸發完整的嵌套實體變更日誌記錄');
            return true;
        } else {
            console.log(`❌ Partner 修改失敗: ${updateResponse.status}`);
            console.log(`   錯誤: ${JSON.stringify(updateResponse.data, null, 2)}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ Partner 修改請求失敗: ${error.message}`);
        return false;
    }
}

/**
 * 測試日誌記錄驗證
 */
async function testLoggingValidation() {
    console.log('\n🔄 測試日誌記錄驗證...');
    
    try {
        // 等待一段時間讓日誌系統處理
        console.log('⏳ 等待日誌系統處理 (3秒)...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 嘗試獲取最近的日誌記錄
        const logsResponse = await makeRequest('GET', '/api/Logging/recent?count=5');
        
        if (logsResponse.status === 200) {
            console.log('✅ 成功獲取日誌記錄');
            
            const logs = logsResponse.data?.data || [];
            console.log(`   獲取到 ${logs.length} 條日誌記錄`);
            
            // 分析日誌內容
            logs.forEach((log, index) => {
                console.log(`\n📋 日誌 ${index + 1}:`);
                console.log(`   時間: ${log.timestamp || 'N/A'}`);
                console.log(`   消息: ${log.message || 'N/A'}`);
                
                if (log.data) {
                    console.log(`   資料類型: ${typeof log.data}`);
                    
                    // 檢查是否包含實體變更資訊
                    if (log.data.entities && Array.isArray(log.data.entities)) {
                        console.log(`   實體變更數量: ${log.data.entities.length}`);
                        
                        log.data.entities.forEach((entity, entityIndex) => {
                            console.log(`     實體 ${entityIndex + 1}: ${entity.entityType} (${entity.entityState})`);
                            
                            // 檢查是否有詳細的變更資料
                            if (entity.data) {
                                console.log(`       包含詳細變更資料: ✅`);
                                
                                // 檢查是否有 before/after 快照
                                if (entity.data.beforeSnapshot && entity.data.afterSnapshot) {
                                    console.log(`       包含前後快照: ✅`);
                                }
                                
                                // 檢查是否有變更詳情
                                if (entity.data.changes) {
                                    const changeCount = Object.keys(entity.data.changes).length;
                                    console.log(`       變更屬性數量: ${changeCount}`);
                                }
                                
                                // 檢查是否有完整階層結構
                                if (entity.data.fullPartnerHierarchy) {
                                    console.log(`       包含完整階層結構: ✅`);
                                }
                            }
                            
                            // 檢查關聯上下文
                            if (entity.relatedContext) {
                                console.log(`       包含關聯上下文: ✅`);
                                console.log(`       上下文類型: ${entity.relatedContext.contextType || 'N/A'}`);
                            }
                        });
                    }
                }
            });
            
            return true;
        } else {
            console.log(`❌ 無法獲取日誌記錄: ${logsResponse.status}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ 日誌驗證請求失敗: ${error.message}`);
        return false;
    }
}

/**
 * 主測試函數
 */
async function runTests() {
    console.log('🚀 開始 FastERP Partner 增強日誌系統測試');
    console.log('=' .repeat(60));
    
    let testResults = {
        creation: false,
        modification: false,
        logging: false
    };
    
    // 測試 1: Partner 創建
    const createdPartner = await testPartnerCreation();
    testResults.creation = !!createdPartner;
    
    // 測試 2: Partner 修改（如果創建成功）
    if (createdPartner && createdPartner.id) {
        testResults.modification = await testPartnerModification(createdPartner.id);
    }
    
    // 測試 3: 日誌記錄驗證
    testResults.logging = await testLoggingValidation();
    
    // 總結測試結果
    console.log('\n' + '=' .repeat(60));
    console.log('📊 測試結果總結:');
    console.log(`   Partner 創建: ${testResults.creation ? '✅ 通過' : '❌ 失敗'}`);
    console.log(`   Partner 修改: ${testResults.modification ? '✅ 通過' : '❌ 失敗'}`);
    console.log(`   日誌記錄驗證: ${testResults.logging ? '✅ 通過' : '❌ 失敗'}`);
    
    const passedTests = Object.values(testResults).filter(result => result).length;
    const totalTests = Object.keys(testResults).length;
    
    console.log(`\n🎯 總體結果: ${passedTests}/${totalTests} 測試通過`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有測試通過！增強的日誌系統運作正常。');
    } else {
        console.log('⚠️  部分測試失敗，請檢查日誌系統配置。');
    }
}

// 執行測試
runTests().catch(error => {
    console.error('❌ 測試執行失敗:', error);
    process.exit(1);
});
